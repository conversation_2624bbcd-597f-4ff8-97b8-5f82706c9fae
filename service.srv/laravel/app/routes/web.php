<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\UserController;
use App\Http\Controllers\AuthController;
use App\Http\Controllers\RobotManagementController;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Hash;

// Rotas de autenticação (públicas)
Route::get('/', [AuthController::class, 'showLoginForm'])->name('login');
Route::get('/login', [AuthController::class, 'showLoginForm'])->name('login');
Route::post('/login', [AuthController::class, 'login']);
Route::post('/logout', [AuthController::class, 'logout'])->name('logout');

// Rota de teste (pública para facilitar testes)
Route::get('/test-login', function () {
    return view('test-login');
});

Route::post('/test-create-users', function () {
    try {
        // Limpar usuários de teste existentes
        \App\Models\User::whereIn('email', ['<EMAIL>', '<EMAIL>'])->delete();

        // Criar usuário administrador
        \App\Models\User::create([
            'token' => Str::random(30),
            'nome' => 'Administrador',
            'email' => '<EMAIL>',
            'senha' => Hash::make('123456'),
            'ativo' => 1,
            'phone' => '(11) 99999-9999',
            'ocupacao' => 'Administrador',
        ]);

        // Criar usuário trader
        \App\Models\User::create([
            'token' => Str::random(30),
            'nome' => 'Trader Demo',
            'email' => '<EMAIL>',
            'senha' => Hash::make('trader123'),
            'ativo' => 1,
            'phone' => '(11) 88888-8888',
            'ocupacao' => 'Trader',
        ]);

        return redirect('/test-login')->with('users_created', true);

    } catch (Exception $e) {
        return redirect('/test-login')->withErrors(['error' => 'Erro ao criar usuários: ' . $e->getMessage()]);
    }
});

// Rotas protegidas por autenticação
Route::middleware(['auth', \App\Http\Middleware\CheckActiveUser::class])->group(function () {

// Rotas de usuário
Route::get('/users', [UserController::class, 'index'])->name('users.index');
Route::get('/users/create', [UserController::class, 'create'])->name('users.create');
Route::post('/users', [UserController::class, 'store'])->name('users.store');
Route::get('/users/{id}/edit', [UserController::class, 'edit'])->name('users.edit');
Route::put('/users/{id}', [UserController::class, 'update'])->name('users.update');

Route::get('/ativos', [App\Http\Controllers\AtivoController::class, 'index'])->name('ativos.index');
Route::get('/ativos/dashboard', [App\Http\Controllers\AtivoController::class, 'dashboard'])->name('ativos.dashboard');
Route::get('/ativos/latest', [App\Http\Controllers\AtivoController::class, 'getLatestData'])->name('ativos.latest');
Route::get('/ativos/extended', [App\Http\Controllers\AtivoController::class, 'getExtendedData'])->name('ativos.extended');
Route::get('/ativos/custom/{count}', [App\Http\Controllers\AtivoController::class, 'getCustomCountData'])->name('ativos.custom');

Route::get('/news/latest', [App\Http\Controllers\NewsController::class, 'getLatestNews'])->name('news.latest');
Route::get('/news/market-news', [App\Http\Controllers\NewsController::class, 'getMarketNews'])->name('news.market');

Route::get('/economic-news', [App\Http\Controllers\EconomicNewsController::class, 'latest'])->name('economic.news');

// Rotas para estatísticas de velas
Route::get('/candles/latest', [App\Http\Controllers\CandlesController::class, 'latest'])->name('candles.latest');
Route::get('/candles/last50', [App\Http\Controllers\CandlesController::class, 'last50'])->name('candles.last50');

// Rotas para gestão dos robôs
Route::get('/robots/management', [App\Http\Controllers\RobotManagementController::class, 'index'])->name('robots.management');
Route::post('/robots/toggle-start23', [App\Http\Controllers\RobotManagementController::class, 'toggleStart23'])->name('robots.toggle-start23');
Route::get('/robots/get-start23-status', [App\Http\Controllers\RobotManagementController::class, 'getStart23Status'])->name('robots.get-start23-status');

// Rotas para gestão de brokers
Route::get('/brokers', [App\Http\Controllers\BrokerController::class, 'index'])->name('brokers.index');
Route::post('/brokers', [App\Http\Controllers\BrokerController::class, 'store'])->name('brokers.store');
Route::get('/brokers/{id}', [App\Http\Controllers\BrokerController::class, 'show'])->name('brokers.show');
Route::put('/brokers/{id}', [App\Http\Controllers\BrokerController::class, 'update'])->name('brokers.update');
Route::delete('/brokers/{id}', [App\Http\Controllers\BrokerController::class, 'destroy'])->name('brokers.destroy');
Route::get('/brokers/api/list', [App\Http\Controllers\BrokerController::class, 'getBrokers'])->name('brokers.api.list');

// Rotas para analytics
Route::get('/analytics', [App\Http\Controllers\AnalyticsController::class, 'index'])->name('analytics.index');
Route::get('/analytics/data', [App\Http\Controllers\AnalyticsController::class, 'getAnalyticsData'])->name('analytics.data');
Route::post('/analytics/export', [App\Http\Controllers\AnalyticsController::class, 'export'])->name('analytics.export');

}); // Fim do grupo de rotas protegidas
