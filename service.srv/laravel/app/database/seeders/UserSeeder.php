<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;
use App\Models\User;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Criar usuário de teste
        User::create([
            'token' => \Str::random(30),
            'nome' => 'Administrador',
            'email' => '<EMAIL>',
            'senha' => Hash::make('123456'),
            'ativo' => 1,
            'phone' => '(11) 99999-9999',
            'ocupacao' => 'Administrador',
        ]);

        // Criar usuário trader
        User::create([
            'token' => \Str::random(30),
            'nome' => 'Trader Demo',
            'email' => '<EMAIL>',
            'senha' => Hash::make('trader123'),
            'ativo' => 1,
            'phone' => '(11) 88888-8888',
            'ocupacao' => 'Trader',
        ]);

        echo "Usuários criados com sucesso!\n";
        echo "Admin: <EMAIL> / 123456\n";
        echo "Trader: <EMAIL> / trader123\n";
    }
}
