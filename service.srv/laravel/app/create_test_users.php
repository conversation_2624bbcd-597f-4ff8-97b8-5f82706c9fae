<?php

// Script para criar usuários de teste
require_once 'vendor/autoload.php';

// Configuração do banco de dados
$host = 'localhost';
$dbname = 'trading_db'; // Ajuste conforme necessário
$username = 'root';     // Ajuste conforme necessário
$password = '';         // Ajuste conforme necessário

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "Conectado ao banco de dados com sucesso!\n";
    
    // Verificar se a tabela existe
    $stmt = $pdo->query("SHOW TABLES LIKE 'tbl_usuarios'");
    if ($stmt->rowCount() == 0) {
        echo "Tabela 'tbl_usuarios' não encontrada!\n";
        exit(1);
    }
    
    // Limpar usuários de teste existentes
    $pdo->exec("DELETE FROM tbl_usuarios WHERE email IN ('<EMAIL>', '<EMAIL>')");
    
    // Criar usuário administrador
    $adminPassword = password_hash('123456', PASSWORD_DEFAULT);
    $adminToken = bin2hex(random_bytes(15));
    
    $stmt = $pdo->prepare("
        INSERT INTO tbl_usuarios (token, nome, email, senha, ativo, phone, ocupacao) 
        VALUES (?, ?, ?, ?, ?, ?, ?)
    ");
    
    $stmt->execute([
        $adminToken,
        'Administrador',
        '<EMAIL>',
        $adminPassword,
        1,
        '(11) 99999-9999',
        'Administrador'
    ]);
    
    echo "✓ Usuário administrador criado: <EMAIL> / 123456\n";
    
    // Criar usuário trader
    $traderPassword = password_hash('trader123', PASSWORD_DEFAULT);
    $traderToken = bin2hex(random_bytes(15));
    
    $stmt->execute([
        $traderToken,
        'Trader Demo',
        '<EMAIL>',
        $traderPassword,
        1,
        '(11) 88888-8888',
        'Trader'
    ]);
    
    echo "✓ Usuário trader criado: <EMAIL> / trader123\n";
    
    // Verificar se os usuários foram criados
    $stmt = $pdo->query("SELECT id_usuario, nome, email, ativo FROM tbl_usuarios WHERE email IN ('<EMAIL>', '<EMAIL>')");
    $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "\n--- Usuários criados ---\n";
    foreach ($users as $user) {
        echo "ID: {$user['id_usuario']} | Nome: {$user['nome']} | Email: {$user['email']} | Ativo: " . ($user['ativo'] ? 'Sim' : 'Não') . "\n";
    }
    
    echo "\n✅ Sistema de login pronto para teste!\n";
    echo "Acesse: http://localhost/login\n";
    
} catch (PDOException $e) {
    echo "Erro na conexão: " . $e->getMessage() . "\n";
    echo "Verifique as configurações do banco de dados no script.\n";
    exit(1);
}
