<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gestão de Brokers</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #1a1a1a;
            color: #ffffff;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .container-fluid {
            padding: 20px;
        }

        .card {
            background-color: #2d2d2d;
            border: 1px solid #404040;
            border-radius: 8px;
        }

        .card-header {
            background-color: #3d3d3d;
            border-bottom: 1px solid #404040;
            color: #ffffff;
        }

        .form-control, .form-select {
            background-color: #404040;
            border: 1px solid #555555;
            color: #ffffff;
        }

        .form-control:focus, .form-select:focus {
            background-color: #404040;
            border-color: #007bff;
            color: #ffffff;
            box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
        }

        .btn-primary {
            background-color: #007bff;
            border-color: #007bff;
        }

        .btn-secondary {
            background-color: #6c757d;
            border-color: #6c757d;
        }

        .btn-warning {
            background-color: #ffc107;
            border-color: #ffc107;
            color: #000;
        }

        .btn-danger {
            background-color: #dc3545;
            border-color: #dc3545;
        }

        .table-dark {
            --bs-table-bg: #2d2d2d;
            --bs-table-border-color: #404040;
        }

        .table-dark th {
            background-color: #3d3d3d;
            border-color: #404040;
        }

        /* Estilos para brokers ativos e inativos */
        .broker-active {
            background-color: #2d2d2d !important;
        }

        .broker-inactive {
            background-color: #404040 !important;
            opacity: 0.7;
        }

        .status-badge {
            font-size: 0.75rem;
            padding: 0.25rem 0.5rem;
        }

        .prop-firm-badge {
            background-color: #17a2b8;
            color: white;
        }

        .regular-broker-badge {
            background-color: #6c757d;
            color: white;
        }

        .alert {
            border-radius: 8px;
        }

        .form-label {
            color: #ffffff;
            font-weight: 500;
        }

        .text-muted {
            color: #adb5bd !important;
        }

        .btn-sm {
            padding: 0.25rem 0.5rem;
            font-size: 0.875rem;
        }

        .table-responsive {
            border-radius: 8px;
        }

        .broker-list-container {
            max-height: 600px;
            overflow-y: auto;
        }

        .broker-list-container::-webkit-scrollbar {
            width: 8px;
        }

        .broker-list-container::-webkit-scrollbar-track {
            background: #2d2d2d;
        }

        .broker-list-container::-webkit-scrollbar-thumb {
            background: #555555;
            border-radius: 4px;
        }

        .broker-list-container::-webkit-scrollbar-thumb:hover {
            background: #666666;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <!-- Botão para voltar ao Dashboard -->
        <div style="position: absolute; top: 15px; left: 20px; z-index: 1000;">
            <a href="{{ route('ativos.dashboard') }}" class="btn btn-info btn-sm">
                <i class="fas fa-arrow-left me-1"></i>
                Voltar ao Dashboard
            </a>
        </div>

        <!-- Botão de Logout -->
        <div style="position: absolute; top: 15px; right: 20px; z-index: 1000;">
            <div class="dropdown">
                <button class="btn btn-outline-light btn-sm dropdown-toggle" type="button" data-bs-toggle="dropdown">
                    <i class="fas fa-user me-1"></i>
                    {{ Auth::user()->nome ?? 'Usuário' }}
                </button>
                <ul class="dropdown-menu dropdown-menu-end">
                    <li>
                        <form method="POST" action="{{ route('logout') }}" class="d-inline">
                            @csrf
                            <button type="submit" class="dropdown-item">
                                <i class="fas fa-sign-out-alt me-2"></i>
                                Sair
                            </button>
                        </form>
                    </li>
                </ul>
            </div>
        </div>

        <div class="row">
            <div class="col-12">
                <h2 class="text-center mb-4">
                    <i class="fas fa-building me-2"></i>
                    Gestão de Brokers
                </h2>
            </div>
        </div>

        <div class="row">
            <!-- Formulário de Cadastro/Edição -->
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-plus-circle me-2"></i>
                            <span id="form-title">Cadastrar Novo Broker</span>
                        </h5>
                    </div>
                    <div class="card-body">
                        <form id="broker-form">
                            <input type="hidden" id="broker-id" name="broker_id">

                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="name" class="form-label">Nome do Broker *</label>
                                    <input type="text" class="form-control" id="name" name="name" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="username" class="form-label">Username *</label>
                                    <input type="text" class="form-control" id="username" name="username" required>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="password" class="form-label">Password *</label>
                                    <input type="text" class="form-control" id="password" name="password" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="opening_date" class="form-label">Data de Abertura *</label>
                                    <input type="date" class="form-control" id="opening_date" name="opening_date" required>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="account_limit" class="form-label">Limite da Conta *</label>
                                    <input type="number" class="form-control" id="account_limit" name="account_limit" step="0.01" min="0" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="account_type" class="form-label">Tipo de Conta *</label>
                                    <select class="form-select" id="account_type" name="account_type" required>
                                        <option value="">Selecione...</option>
                                        <option value="Demo">Demo</option>
                                        <option value="Real">Real</option>
                                    </select>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="is_active" class="form-label">Status *</label>
                                    <select class="form-select" id="is_active" name="is_active" required>
                                        <option value="">Selecione...</option>
                                        <option value="1">Ativo</option>
                                        <option value="0">Inativo</option>
                                    </select>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="is_prop_firm" class="form-label">É Prop Firm? *</label>
                                    <select class="form-select" id="is_prop_firm" name="is_prop_firm" required>
                                        <option value="">Selecione...</option>
                                        <option value="1">Sim</option>
                                        <option value="0">Não</option>
                                    </select>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="prop_status" class="form-label">Status Prop Firm</label>
                                    <select class="form-select" id="prop_status" name="prop_status">
                                        <option value="">Não se aplica</option>
                                        <option value="Approved">Aprovado</option>
                                        <option value="Rejected">Rejeitado</option>
                                    </select>
                                    <small class="text-muted">Apenas para Prop Firms</small>
                                </div>
                            </div>

                            <div class="d-flex gap-2">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-1"></i>
                                    <span id="btn-text">Salvar</span>
                                </button>
                                <button type="button" class="btn btn-secondary" id="btn-cancel" onclick="resetForm()">
                                    <i class="fas fa-times me-1"></i>
                                    Cancelar
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Lista de Brokers -->
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-list me-2"></i>
                            Lista de Brokers
                        </h5>
                        <button class="btn btn-sm btn-outline-light" onclick="loadBrokers()">
                            <i class="fas fa-sync-alt"></i>
                        </button>
                    </div>
                    <div class="card-body p-0">
                        <div class="broker-list-container">
                            <div class="table-responsive">
                                <table class="table table-dark table-hover mb-0">
                                    <thead>
                                        <tr>
                                            <th>Nome</th>
                                            <th>Senha</th>
                                            <th>Tipo</th>
                                            <th>Status</th>
                                            <th>Ações</th>
                                        </tr>
                                    </thead>
                                    <tbody id="brokers-list">
                                        <!-- Os brokers serão carregados via JavaScript -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

    <script>
        $(document).ready(function() {
            loadBrokers();

            // Controlar visibilidade do campo prop_status
            $('#is_prop_firm').change(function() {
                const isPropFirm = $(this).val() === '1';
                const propStatusField = $('#prop_status').closest('.col-md-6');

                if (isPropFirm) {
                    propStatusField.show();
                } else {
                    propStatusField.hide();
                    $('#prop_status').val('');
                }
            });

            // Submit do formulário
            $('#broker-form').submit(function(e) {
                e.preventDefault();
                saveBroker();
            });
        });

        // Função para carregar a lista de brokers
        function loadBrokers() {
            $.ajax({
                url: '{{ route("brokers.api.list") }}',
                type: 'GET',
                dataType: 'json',
                success: function(response) {
                    if (response.success) {
                        updateBrokersList(response.brokers);
                    } else {
                        showAlert('Erro ao carregar brokers: ' + response.message, 'danger');
                    }
                },
                error: function(xhr, status, error) {
                    console.error('Erro ao carregar brokers:', error);
                    showAlert('Erro de comunicação com o servidor', 'danger');
                }
            });
        }

        // Função para atualizar a lista de brokers
        function updateBrokersList(brokers) {
            const tbody = $('#brokers-list');
            tbody.empty();

            if (brokers.length === 0) {
                tbody.append(`
                    <tr>
                        <td colspan="5" class="text-center text-muted">
                            <i class="fas fa-inbox me-2"></i>
                            Nenhum broker cadastrado
                        </td>
                    </tr>
                `);
                return;
            }

            brokers.forEach(function(broker) {
                const isActive = broker.is_active == 1;
                const rowClass = isActive ? 'broker-active' : 'broker-inactive';
                const statusBadge = isActive ?
                    '<span class="badge bg-success status-badge">Ativo</span>' :
                    '<span class="badge bg-secondary status-badge">Inativo</span>';

                const typeBadge = broker.is_prop_firm == 1 ?
                    '<span class="badge prop-firm-badge">Prop Firm</span>' :
                    '<span class="badge regular-broker-badge">Regular</span>';

                const propStatus = broker.is_prop_firm == 1 && broker.prop_status ?
                    `<br><small class="text-muted">${broker.prop_status}</small>` : '';

                const row = `
                    <tr class="${rowClass}">
                        <td>
                            <strong>${broker.name}</strong>
                            <br><small class="text-muted">${broker.username}</small>
                        </td>
                        <td>
                            <code style="background-color: #404040; color: #fff; padding: 2px 6px; border-radius: 3px; font-size: 0.85em;">
                                ${broker.password}
                            </code>
                        </td>
                        <td>
                            ${typeBadge}
                            <br><small class="text-muted">${broker.account_type}</small>
                            ${propStatus}
                        </td>
                        <td>
                            ${statusBadge}
                            <br><small class="text-muted">R$ ${parseFloat(broker.account_limit).toLocaleString('pt-BR', {minimumFractionDigits: 2})}</small>
                        </td>
                        <td>
                            <div class="btn-group-vertical btn-group-sm">
                                <button class="btn btn-warning btn-sm" onclick="editBroker(${broker.id})" title="Editar">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="btn btn-danger btn-sm" onclick="deleteBroker(${broker.id}, '${broker.name}')" title="Excluir">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                `;

                tbody.append(row);
            });
        }

        // Função para salvar broker (criar ou atualizar)
        function saveBroker() {
            const formData = {
                name: $('#name').val(),
                username: $('#username').val(),
                password: $('#password').val(),
                opening_date: $('#opening_date').val(),
                is_active: $('#is_active').val(),
                account_limit: $('#account_limit').val(),
                account_type: $('#account_type').val(),
                is_prop_firm: $('#is_prop_firm').val(),
                prop_status: $('#prop_status').val() || null,
                _token: '{{ csrf_token() }}'
            };

            const brokerId = $('#broker-id').val();
            const isEditing = brokerId !== '';

            const url = isEditing ?
                '{{ route("brokers.update", ":id") }}'.replace(':id', brokerId) :
                '{{ route("brokers.store") }}';

            const method = isEditing ? 'PUT' : 'POST';

            $.ajax({
                url: url,
                type: method,
                data: formData,
                dataType: 'json',
                success: function(response) {
                    if (response.success) {
                        showAlert(response.message, 'success');
                        resetForm();
                        loadBrokers();
                    } else {
                        if (response.errors) {
                            let errorMessages = '';
                            Object.keys(response.errors).forEach(function(key) {
                                errorMessages += response.errors[key].join(', ') + '<br>';
                            });
                            showAlert(errorMessages, 'danger');
                        } else {
                            showAlert(response.message, 'danger');
                        }
                    }
                },
                error: function(xhr, status, error) {
                    console.error('Erro ao salvar broker:', error);
                    showAlert('Erro de comunicação com o servidor', 'danger');
                }
            });
        }

        // Função para editar broker
        function editBroker(id) {
            $.ajax({
                url: '{{ route("brokers.show", ":id") }}'.replace(':id', id),
                type: 'GET',
                dataType: 'json',
                success: function(response) {
                    if (response.success) {
                        const broker = response.broker;

                        // Preencher o formulário
                        $('#broker-id').val(broker.id);
                        $('#name').val(broker.name);
                        $('#username').val(broker.username);
                        $('#password').val(broker.password);
                        $('#opening_date').val(broker.opening_date);
                        $('#is_active').val(broker.is_active.toString());
                        $('#account_limit').val(broker.account_limit);
                        $('#account_type').val(broker.account_type);
                        $('#is_prop_firm').val(broker.is_prop_firm.toString());
                        $('#prop_status').val(broker.prop_status || '');

                        // Controlar visibilidade do campo prop_status
                        const propStatusField = $('#prop_status').closest('.col-md-6');
                        if (broker.is_prop_firm == 1) {
                            propStatusField.show();
                        } else {
                            propStatusField.hide();
                        }

                        // Alterar título do formulário
                        $('#form-title').text('Editar Broker');
                        $('#btn-text').text('Atualizar');

                        // Scroll para o formulário
                        $('html, body').animate({
                            scrollTop: $('#broker-form').offset().top - 100
                        }, 500);

                    } else {
                        showAlert('Erro ao carregar dados do broker: ' + response.message, 'danger');
                    }
                },
                error: function(xhr, status, error) {
                    console.error('Erro ao carregar broker:', error);
                    showAlert('Erro de comunicação com o servidor', 'danger');
                }
            });
        }

        // Função para excluir broker
        function deleteBroker(id, name) {
            if (confirm(`Tem certeza que deseja excluir o broker "${name}"?`)) {
                $.ajax({
                    url: '{{ route("brokers.destroy", ":id") }}'.replace(':id', id),
                    type: 'DELETE',
                    data: {
                        _token: '{{ csrf_token() }}'
                    },
                    dataType: 'json',
                    success: function(response) {
                        if (response.success) {
                            showAlert(response.message, 'success');
                            loadBrokers();
                        } else {
                            showAlert('Erro ao excluir broker: ' + response.message, 'danger');
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error('Erro ao excluir broker:', error);
                        showAlert('Erro de comunicação com o servidor', 'danger');
                    }
                });
            }
        }

        // Função para resetar o formulário
        function resetForm() {
            $('#broker-form')[0].reset();
            $('#broker-id').val('');
            $('#form-title').text('Cadastrar Novo Broker');
            $('#btn-text').text('Salvar');

            // Esconder campo prop_status
            $('#prop_status').closest('.col-md-6').hide();

            // Remover alertas
            $('.alert').remove();
        }

        // Função para mostrar alertas
        function showAlert(message, type) {
            // Remover alertas existentes
            $('.alert').remove();

            const alertHtml = `
                <div class="alert alert-${type} alert-dismissible fade show" role="alert">
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;

            // Adicionar alerta no topo da página
            $('.container-fluid').prepend(alertHtml);

            // Auto-remover após 5 segundos para alertas de sucesso
            if (type === 'success') {
                setTimeout(function() {
                    $('.alert-success').fadeOut();
                }, 5000);
            }
        }
    </script>
</body>
</html>
