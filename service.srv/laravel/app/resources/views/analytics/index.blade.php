<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Trading Journal - Analytics</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body {
            background-color: #1a1a1a;
            color: #ffffff;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .navbar {
            background-color: #2d2d2d;
            border-bottom: 1px solid #404040;
            padding: 15px 0;
        }

        .navbar-brand {
            color: #ffffff !important;
            font-size: 1.5rem;
            font-weight: 600;
        }

        .filter-section {
            background-color: #2d2d2d;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            border: 1px solid #404040;
        }

        .stats-card {
            background-color: #2d2d2d;
            border: 1px solid #404040;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            height: 120px;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        .stats-value {
            font-size: 1.8rem;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .stats-label {
            font-size: 0.9rem;
            color: #adb5bd;
        }

        .stats-badge {
            font-size: 0.75rem;
            padding: 2px 8px;
            border-radius: 12px;
            margin-left: 8px;
        }

        .positive {
            color: #1db954;
        }

        .negative {
            color: #f45b5b;
        }

        .neutral {
            color: #ffc107;
        }

        .chart-container {
            background-color: #2d2d2d;
            border: 1px solid #404040;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }

        .chart-title {
            font-size: 1.2rem;
            font-weight: 600;
            margin-bottom: 15px;
            color: #ffffff;
        }

        .btn-filter {
            background-color: #404040;
            border: 1px solid #555555;
            color: #ffffff;
            border-radius: 4px;
            padding: 8px 16px;
            margin-right: 10px;
            margin-bottom: 10px;
        }

        .btn-filter:hover {
            background-color: #555555;
            color: #ffffff;
        }

        .btn-filter.active {
            background-color: #007bff;
            border-color: #007bff;
        }

        .form-select {
            background-color: #404040;
            border: 1px solid #555555;
            color: #ffffff;
        }

        .form-select:focus {
            background-color: #404040;
            border-color: #007bff;
            color: #ffffff;
            box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
        }

        .donut-chart {
            position: relative;
            width: 200px;
            height: 200px;
            margin: 0 auto;
        }

        .assets-stats {
            background-color: #2d2d2d;
            border: 1px solid #404040;
            border-radius: 8px;
            padding: 20px;
        }

        .asset-stat-item {
            margin-bottom: 20px;
        }

        .asset-stat-title {
            font-size: 1rem;
            font-weight: 600;
            margin-bottom: 10px;
        }

        .asset-stat-subtitle {
            font-size: 0.8rem;
            color: #adb5bd;
            margin-bottom: 15px;
        }

        .logout-btn {
            position: absolute;
            top: 15px;
            right: 20px;
            z-index: 1000;
        }

        .nav-buttons {
            position: absolute;
            top: 15px;
            left: 20px;
            z-index: 1000;
        }

        .dropdown-menu {
            background-color: #2d2d2d;
            border: 1px solid #404040;
        }

        .dropdown-item {
            color: #ffffff;
        }

        .dropdown-item:hover {
            background-color: #404040;
            color: #ffffff;
        }

        @media (max-width: 768px) {
            .stats-card {
                margin-bottom: 10px;
            }

            .nav-buttons, .logout-btn {
                position: relative;
                top: auto;
                left: auto;
                right: auto;
                margin-bottom: 20px;
            }

            .donut-chart {
                width: 150px;
                height: 150px;
            }

            .container-fluid {
                padding: 10px;
            }
        }
    </style>
</head>
<body>
    <!-- Botões de Navegação -->
    <div class="nav-buttons">
        <a href="{{ route('ativos.index') }}" class="btn btn-secondary btn-sm me-2">
            <i class="fas fa-arrow-left me-1"></i>
            Voltar
        </a>
        <a href="{{ route('ativos.dashboard') }}" class="btn btn-info btn-sm me-2">
            <i class="fas fa-chart-line me-1"></i>
            Dashboard
        </a>
        <a href="{{ route('brokers.index') }}" class="btn btn-success btn-sm">
            <i class="fas fa-building me-1"></i>
            Brokers
        </a>
    </div>

    <!-- Botão de Logout -->
    <div class="logout-btn">
        <div class="dropdown">
            <button class="btn btn-outline-light btn-sm dropdown-toggle" type="button" data-bs-toggle="dropdown">
                <i class="fas fa-user me-1"></i>
                {{ Auth::user()->nome ?? 'Usuário' }}
            </button>
            <ul class="dropdown-menu dropdown-menu-end">
                <li>
                    <form method="POST" action="{{ route('logout') }}" class="d-inline">
                        @csrf
                        <button type="submit" class="dropdown-item">
                            <i class="fas fa-sign-out-alt me-2"></i>
                            Sair
                        </button>
                    </form>
                </li>
            </ul>
        </div>
    </div>

    <div class="container-fluid mt-5 pt-3">
        <!-- Header -->
        <div class="row mb-4">
            <div class="col-12">
                <h1 class="mb-0">Trading Journal</h1>
                <p class="text-muted">Análise completa de performance de trading</p>
            </div>
        </div>

        <!-- Filtros -->
        <div class="filter-section">
            <div class="row">
                <div class="col-md-2">
                    <select class="form-select">
                        <option>All Accounts</option>
                        <option>Demo Account</option>
                        <option>Live Account</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <select class="form-select">
                        <option>All Assets</option>
                        <option>EUR/USD</option>
                        <option>GBP/USD</option>
                        <option>USD/JPY</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <select class="form-select">
                        <option>All Results</option>
                        <option>Profitable</option>
                        <option>Loss</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <select class="form-select">
                        <option>All Moves</option>
                        <option>Long</option>
                        <option>Short</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <select class="form-select">
                        <option>All Strategies</option>
                        <option>Scalping</option>
                        <option>Day Trading</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <button class="btn btn-success w-100">
                        <i class="fas fa-filter me-1"></i>
                        Apply
                    </button>
                </div>
            </div>
        </div>

        <!-- Cards de Estatísticas -->
        <div class="row mb-4">
            <div class="col-md-2">
                <div class="stats-card">
                    <div class="stats-value positive">$8400 <span class="stats-badge bg-success">Profitable</span></div>
                    <div class="stats-label">Net Profit</div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="stats-card">
                    <div class="stats-value positive">1.21 <span class="stats-badge bg-success">Profitable</span></div>
                    <div class="stats-label">Sharpe Ratio</div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="stats-card">
                    <div class="stats-value neutral">70.2%</div>
                    <div class="stats-label">Win Rate</div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="stats-card">
                    <div class="stats-value neutral">1:1.32</div>
                    <div class="stats-label">Risk/Reward Ratio</div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="stats-card">
                    <div class="stats-value positive">1.37</div>
                    <div class="stats-label">Profit Factor</div>
                </div>
            </div>
        </div>

        <!-- Segunda linha de estatísticas -->
        <div class="row mb-4">
            <div class="col-md-2">
                <div class="stats-card">
                    <div class="stats-value positive">$4421.3</div>
                    <div class="stats-label">Best Trade</div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="stats-card">
                    <div class="stats-value negative">-$170.2</div>
                    <div class="stats-label">Worst Trade</div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="stats-card">
                    <div class="stats-value neutral">30 Trades</div>
                    <div class="stats-label">Total Trades</div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="stats-card">
                    <div class="stats-value neutral">00h 24m 12s</div>
                    <div class="stats-label">Avg. Duration</div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="stats-card">
                    <div class="stats-value neutral">$22.51</div>
                    <div class="stats-label">Total Commissions</div>
                </div>
            </div>
        </div>

        <!-- Botões de filtro de dados -->
        <div class="row mb-3">
            <div class="col-12">
                <p class="mb-2">More detailed data from current filter:</p>
                <button class="btn btn-filter active">Trades <span class="badge bg-secondary">8</span></button>
                <button class="btn btn-filter">Statistics <i class="fas fa-chart-bar"></i></button>
                <button class="btn btn-filter">Calendar <i class="fas fa-calendar"></i></button>
            </div>
        </div>

        <!-- Gráfico de Performance -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="chart-container">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <div>
                            <h5 class="chart-title mb-1">Performance Stats (PnL)</h5>
                            <small class="text-muted">How has my performance been over time by <strong>Balance</strong></small>
                        </div>
                        <div>
                            <button class="btn btn-sm btn-outline-success">Target</button>
                        </div>
                    </div>
                    <canvas id="performanceChart" height="100"></canvas>
                    <div class="row mt-3">
                        <div class="col-3 text-center">
                            <small class="text-muted">Highest Profit: <span class="positive">$4421.3</span></small><br>
                            <small class="text-muted">On 08:31 14th May 2023</small>
                        </div>
                        <div class="col-3 text-center">
                            <small class="text-muted">Highest Balance: <span class="positive">$9,119</span></small><br>
                            <small class="text-muted">On 10:29 18th May 2023</small>
                        </div>
                        <div class="col-3 text-center">
                            <small class="text-muted">Lowest Balance: <span class="negative">$4,107</span></small><br>
                            <small class="text-muted">On 08:52 2nd May 2023</small>
                        </div>
                        <div class="col-3 text-center">
                            <small class="text-muted">Current Balance: <span class="positive">$7,489</span></small><br>
                            <small class="text-muted">On 11:01 3rd May 2023</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Assets Stats -->
        <div class="row">
            <div class="col-12">
                <div class="assets-stats">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h5 class="chart-title mb-0">Assets Stats</h5>
                        <small class="text-muted">Learn how to use <i class="fas fa-question-circle"></i></small>
                    </div>

                    <div class="row">
                        <div class="col-md-3">
                            <div class="asset-stat-item">
                                <div class="asset-stat-title">Market Allocation</div>
                                <div class="asset-stat-subtitle">In which markets do I trade the most.</div>
                                <div class="donut-chart">
                                    <canvas id="marketChart" width="200" height="200"></canvas>
                                </div>
                                <div class="mt-2">
                                    <small><span style="color: #1db954;">●</span> Forex</small><br>
                                    <small><span style="color: #ffc107;">●</span> Crypto</small><br>
                                    <small><span style="color: #007bff;">●</span> Stocks</small>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-3">
                            <div class="asset-stat-item">
                                <div class="asset-stat-title">Asset Allocation</div>
                                <div class="asset-stat-subtitle">How much do I trade each asset.</div>
                                <div class="donut-chart">
                                    <canvas id="assetChart" width="200" height="200"></canvas>
                                </div>
                                <div class="mt-2">
                                    <small><span style="color: #1db954;">●</span> EUR/USD</small><br>
                                    <small><span style="color: #ffc107;">●</span> GBP/USD</small><br>
                                    <small><span style="color: #007bff;">●</span> USD/JPY</small>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-3">
                            <div class="asset-stat-item">
                                <div class="asset-stat-title">Profit Contribution</div>
                                <div class="asset-stat-subtitle">What are the assets where I win the most.</div>
                                <div class="donut-chart">
                                    <canvas id="profitChart" width="200" height="200"></canvas>
                                </div>
                                <div class="mt-2">
                                    <small><span style="color: #1db954;">●</span> EUR/USD</small><br>
                                    <small><span style="color: #28a745;">●</span> GBP/USD</small><br>
                                    <small><span style="color: #20c997;">●</span> USD/JPY</small>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-3">
                            <div class="asset-stat-item">
                                <div class="asset-stat-title">Losses Contribution</div>
                                <div class="asset-stat-subtitle">What are the assets where I lose the most.</div>
                                <div class="donut-chart">
                                    <canvas id="lossChart" width="200" height="200"></canvas>
                                </div>
                                <div class="mt-2">
                                    <small><span style="color: #dc3545;">●</span> USD/JPY</small><br>
                                    <small><span style="color: #fd7e14;">●</span> EUR/USD</small><br>
                                    <small><span style="color: #e83e8c;">●</span> GBP/USD</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Gráfico de Performance
        const ctx = document.getElementById('performanceChart').getContext('2d');
        const performanceChart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: ['May 1', 'May 5', 'May 10', 'May 15', 'May 20', 'May 25', 'May 30'],
                datasets: [{
                    label: 'Balance',
                    data: [5000, 4200, 5800, 7200, 6800, 8400, 7489],
                    borderColor: '#1db954',
                    backgroundColor: 'rgba(29, 185, 84, 0.1)',
                    borderWidth: 2,
                    fill: true,
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: false,
                        grid: {
                            color: '#404040'
                        },
                        ticks: {
                            color: '#adb5bd',
                            callback: function(value) {
                                return '$' + value;
                            }
                        }
                    },
                    x: {
                        grid: {
                            color: '#404040'
                        },
                        ticks: {
                            color: '#adb5bd'
                        }
                    }
                }
            }
        });

        // Gráficos Donut
        function createDonutChart(canvasId, data, colors) {
            const ctx = document.getElementById(canvasId).getContext('2d');
            return new Chart(ctx, {
                type: 'doughnut',
                data: {
                    datasets: [{
                        data: data,
                        backgroundColor: colors,
                        borderWidth: 0
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: true,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    cutout: '70%'
                }
            });
        }

        // Market Allocation
        createDonutChart('marketChart', [60, 25, 15], ['#1db954', '#ffc107', '#007bff']);

        // Asset Allocation
        createDonutChart('assetChart', [45, 35, 20], ['#1db954', '#ffc107', '#007bff']);

        // Profit Contribution
        createDonutChart('profitChart', [50, 30, 20], ['#1db954', '#28a745', '#20c997']);

        // Losses Contribution
        createDonutChart('lossChart', [40, 35, 25], ['#dc3545', '#fd7e14', '#e83e8c']);
    </script>
</body>
</html>
