<!DOCTYPE html>
<html lang="pt-BR">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Dashboard de Trading</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/css/bootstrap.min.css" rel="stylesheet" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" />
    <style>
        body {
            background-color: #1a1a1a;
            color: #ffffff;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .card {
            background-color: #2d2d2d;
            border: 1px solid #444;
            border-radius: 8px;
        }

        .card-title {
            color: #ffffff;
            font-weight: 600;
        }

        .table-dark {
            background-color: #2d2d2d;
        }

        .table-dark th,
        .table-dark td {
            border-color: #444;
            color: #ffffff;
        }

        .table-hover tbody tr:hover {
            background-color: #3a3a3a;
        }

        .btn-info {
            background-color: #17a2b8;
            border-color: #17a2b8;
        }

        .btn-outline-info {
            color: #17a2b8;
            border-color: #17a2b8;
        }

        .btn-outline-info:hover {
            background-color: #17a2b8;
            border-color: #17a2b8;
        }

        /* Notificação de Trading */
        .trading-notification {
            position: fixed;
            top: 20px;
            left: 20px;
            z-index: 9999;
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
            color: white;
            padding: 15px 20px;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.3);
            font-weight: bold;
            font-size: 14px;
            max-width: 350px;
            animation: slideIn 0.5s ease-out;
        }

        @keyframes slideIn {
            from {
                transform: translateX(-100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        .trading-notification-pulse {
            animation: pulse 2s infinite;
        }

        /* Estilos para tabela de spreads */
        .spread-table {
            font-size: 0.9em;
        }

        .spread-very-affected {
            background-color: rgba(255, 107, 107, 0.2) !important;
        }

        .spread-moderately-affected {
            background-color: rgba(255, 193, 7, 0.2) !important;
        }

        .spread-little-affected {
            background-color: rgba(40, 167, 69, 0.2) !important;
        }

        .currency-pair {
            font-weight: bold;
            color: #17a2b8;
        }

        .spread-value {
            font-family: 'Courier New', monospace;
            font-size: 0.85em;
        }

        .observation-badge {
            font-size: 0.75em;
            padding: 4px 8px;
            border-radius: 12px;
        }

        .obs-muito-afetado {
            background-color: #dc3545;
            color: white;
        }

        .obs-moderadamente-afetado {
            background-color: #ffc107;
            color: #212529;
        }

        .obs-pouco-afetado {
            background-color: #28a745;
            color: white;
        }



        .no-scroll-table-container {
            overflow: hidden;
        }

        .table-responsive {
            overflow-x: hidden;
            overflow-y: hidden;
        }

        /* Estilos para os relógios digitais */
        .clocks-container {
            position: absolute;
            top: 15px;
            right: 20px;
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
            max-width: 400px;
            justify-content: flex-end;
        }

        .digital-clock {
            display: flex;
            align-items: center;
            font-size: 0.9rem;
            color: #fff;
            margin-bottom: 5px;
        }

        .digital-clock img {
            width: 20px;
            height: 14px;
            margin-right: 5px;
        }

        /* Estilo para a tabela de notícias econômicas */
        #economic-news-table td {
            white-space: normal;
            word-wrap: break-word;
        }

        #economic-news-table th:nth-child(4),
        #economic-news-table td:nth-child(4) {
            max-width: 150px;
            font-size: 0.7rem;
            color: #9ea5a5;
            word-break: break-word;
        }

        /* Estilo para as bolinhas de impacto */
        #economic-news-table .fa-circle {
            font-size: 0.5rem;
            margin-right: 2px;
        }

        /* Estilo para eventos próximos da hora atual */
        #economic-news-table tr.event-near-time,
        #economic-news-table tr.event-near-time td {
            background-color: #1f5305 !important;
        }

        /* Cores para os valores de impacto */
        .positive-value {
            color: #1db954 !important;
        }

        .negative-value {
            color: #f45b5b !important;
        }

        .valor-zero {
            color: #ffc107 !important;
        }
    </style>
</head>

<body>
    <!-- Notificação de Horário de Trading -->
    <div id="trading-notification" style="display: none;" class="trading-notification">
        <div style="display: flex; align-items: center; gap: 10px;">
            <i class="fas fa-exclamation-triangle" style="font-size: 18px; color: #ffd700;"></i>
            <div>
                <div id="notification-title" style="font-size: 16px; margin-bottom: 5px;"></div>
                <div id="notification-message" style="font-size: 12px; opacity: 0.9;"></div>
            </div>
            <button onclick="closeTradingNotification()" style="background: none; border: none; color: white; font-size: 18px; cursor: pointer; margin-left: auto;">×</button>
        </div>
    </div>



    <div class="container-fluid mt-4">
        <div class="row">
            <div class="col-12">
                <!-- Botão para voltar -->
                <div style="position: absolute; top: 15px; left: 20px; z-index: 1000;">
                    <a href="{{ route('ativos.index') }}" class="btn btn-secondary btn-sm">
                        <i class="fas fa-arrow-left me-1"></i>
                        Voltar
                    </a>
                </div>

                <!-- Relógios digitais Brasil e Portugal -->
                <div class="clocks-container">
                    <!-- Relógio Brasil -->
                    <div class="digital-clock">
                        <img src="https://flagcdn.com/br.svg" alt="Bandeira do Brasil">
                        <span id="brazil-time">00:00:00</span>
                    </div>

                    <!-- Relógio Portugal -->
                    <div class="digital-clock">
                        <img src="https://flagcdn.com/pt.svg" alt="Bandeira de Portugal">
                        <span id="portugal-time">00:00:00</span>
                    </div>

                    <!-- Relógio EUA (Nova York) -->
                    <div class="digital-clock">
                        <img src="https://flagcdn.com/us.svg" alt="Bandeira dos EUA">
                        <span id="usa-time">00:00:00</span>
                    </div>

                    <!-- Relógio Reino Unido (Londres) -->
                    <div class="digital-clock">
                        <img src="https://flagcdn.com/gb.svg" alt="Bandeira do Reino Unido">
                        <span id="uk-time">00:00:00</span>
                    </div>
                </div>

                <h1 class="text-center mb-4">Dashboard de Trading</h1>

                <!-- Tabela de spreads -->
                <div class="row justify-content-end">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-body">
                                <h4 class="card-title mb-3">
                                    <i class="fas fa-chart-line me-2"></i>
                                    Spreads dos Pares de Moedas
                                </h4>

                                <!-- Legenda -->
                                <div class="mb-3">
                                    <small class="text-muted">
                                        🔴 Muito afetado &nbsp;&nbsp;
                                        🟡 Moderadamente afetado &nbsp;&nbsp;
                                        🟢 Pouco afetado
                                    </small>
                                </div>

                                <div class="table-responsive">
                                    <table class="table table-dark table-hover spread-table" id="spreads-table">
                                        <thead>
                                            <tr>
                                                <th style="width: 20%;">Par</th>
                                                <th style="width: 20%;">Antes</th>
                                                <th style="width: 20%;">Durante</th>
                                                <th style="width: 20%;">Após</th>
                                                <th style="width: 20%;">Status</th>
                                            </tr>
                                        </thead>
                                        <tbody id="spreads-body">
                                            <!-- Os dados serão carregados via JavaScript -->
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Notícias Econômicas -->
                <div class="row justify-content-end mt-4">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-body">
                                <h4 class="card-title mb-3">
                                    <i class="fas fa-newspaper me-2"></i>
                                    Notícias Econômicas
                                </h4>
                                <div class="table-responsive">
                                    <table class="table table-dark table-hover" id="economic-news-table">
                                        <thead>
                                            <tr>
                                                <th>Hora</th>
                                                <th>Moeda</th>
                                                <th>Impacto</th>
                                                <th>Evento</th>
                                            </tr>
                                        </thead>
                                        <tbody id="economic-news-body">
                                            <!-- Os dados serão carregados via JavaScript -->
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Dados dos spreads dos pares de moedas
        const spreadsData = [
            {
                "currencyPair": "AUD/USD",
                "spreadBeforePause_2150_2159": "0.8 – 1.5",
                "spreadDuringPause_2200_2300": "10 – 30+",
                "spreadAfterPause_2305_2315": "1.0 – 2.0",
                "observation": "Muito afetado"
            },
            {
                "currencyPair": "NZD/USD",
                "spreadBeforePause_2150_2159": "1.0 – 1.8",
                "spreadDuringPause_2200_2300": "12 – 35+",
                "spreadAfterPause_2305_2315": "1.2 – 2.5",
                "observation": "Muito afetado"
            },
            {
                "currencyPair": "AUD/JPY",
                "spreadBeforePause_2150_2159": "1.0 – 2.0",
                "spreadDuringPause_2200_2300": "15 – 40+",
                "spreadAfterPause_2305_2315": "1.5 – 3.0",
                "observation": "Muito afetado"
            },
            {
                "currencyPair": "GBP/JPY",
                "spreadBeforePause_2150_2159": "1.2 – 2.5",
                "spreadDuringPause_2200_2300": "8 – 25+",
                "spreadAfterPause_2305_2315": "1.5 – 3.0",
                "observation": "Moderadamente afetado"
            },
            {
                "currencyPair": "EUR/JPY",
                "spreadBeforePause_2150_2159": "1.0 – 2.0",
                "spreadDuringPause_2200_2300": "8 – 20+",
                "spreadAfterPause_2305_2315": "1.2 – 2.5",
                "observation": "Moderadamente afetado"
            },
            {
                "currencyPair": "EUR/USD",
                "spreadBeforePause_2150_2159": "0.5 – 1.0",
                "spreadDuringPause_2200_2300": "2 – 5",
                "spreadAfterPause_2305_2315": "0.7 – 1.2",
                "observation": "Pouco afetado"
            },
            {
                "currencyPair": "GBP/USD",
                "spreadBeforePause_2150_2159": "0.6 – 1.0",
                "spreadDuringPause_2200_2300": "3 – 6",
                "spreadAfterPause_2305_2315": "0.8 – 1.3",
                "observation": "Pouco afetado"
            },
            {
                "currencyPair": "USD/JPY",
                "spreadBeforePause_2150_2159": "0.5 – 1.2",
                "spreadDuringPause_2200_2300": "3 – 7",
                "spreadAfterPause_2305_2315": "0.7 – 1.5",
                "observation": "Pouco afetado"
            },
            {
                "currencyPair": "USD/CAD",
                "spreadBeforePause_2150_2159": "1.0 – 1.8",
                "spreadDuringPause_2200_2300": "6 – 15+",
                "spreadAfterPause_2305_2315": "1.2 – 2.5",
                "observation": "Moderadamente afetado"
            },
            {
                "currencyPair": "USD/CHF",
                "spreadBeforePause_2150_2159": "1.0 – 1.5",
                "spreadDuringPause_2200_2300": "4 – 10+",
                "spreadAfterPause_2305_2315": "1.0 – 2.0",
                "observation": "Moderadamente afetado"
            },
            {
                "currencyPair": "EUR/GBP",
                "spreadBeforePause_2150_2159": "0.7 – 1.3",
                "spreadDuringPause_2200_2300": "3 – 7",
                "spreadAfterPause_2305_2315": "0.9 – 1.5",
                "observation": "Pouco afetado"
            }
        ];

        // Função para atualizar os relógios digitais
        function updateClocks() {
            const now = new Date();

            // Configurar relógio do Brasil (UTC-3)
            const brazilTime = new Date(now.getTime());
            brazilTime.setHours(now.getUTCHours() - 3);

            // Configurar relógio de Portugal (UTC+0 no horário padrão, UTC+1 no horário de verão)
            const portugalTime = new Date(now.getTime());
            const currentMonth = now.getUTCMonth() + 1;
            const isDST = currentMonth >= 3 && currentMonth <= 10;

            if (isDST) {
                portugalTime.setHours(now.getUTCHours() + 1);
            } else {
                portugalTime.setHours(now.getUTCHours());
            }

            // Configurar relógio dos EUA (Nova York)
            const usaTime = new Date(now.getTime());
            if (isDST) {
                usaTime.setHours(now.getUTCHours() - 4);
            } else {
                usaTime.setHours(now.getUTCHours() - 5);
            }

            // Configurar relógio do Reino Unido (Londres)
            const ukTime = new Date(now.getTime());
            if (isDST) {
                ukTime.setHours(now.getUTCHours() + 1);
            } else {
                ukTime.setHours(now.getUTCHours());
            }

            function formatTime(date) {
                const hours = date.getHours() < 10 ? '0' + date.getHours() : date.getHours();
                const minutes = date.getMinutes() < 10 ? '0' + date.getMinutes() : date.getMinutes();
                const seconds = date.getSeconds() < 10 ? '0' + date.getSeconds() : date.getSeconds();
                return hours + ':' + minutes + ':' + seconds;
            }

            $('#brazil-time').text(formatTime(brazilTime));
            $('#portugal-time').text(formatTime(portugalTime));
            $('#usa-time').text(formatTime(usaTime));
            $('#uk-time').text(formatTime(ukTime));

            // Verificar notificações de trading
            checkTradingNotifications(portugalTime);

            setTimeout(updateClocks, 1000);
        }

        // Função para verificar notificações de trading
        function checkTradingNotifications(portugalTime) {
            const hours = portugalTime.getHours();
            const minutes = portugalTime.getMinutes();
            const currentTimeMinutes = hours * 60 + minutes;

            // Horários críticos em minutos (horário de Portugal)
            const criticalTimes = [
                { start: 21 * 60 + 45, end: 22 * 60 + 4, period: "Antes da Pausa", time: "21:50-21:59" },
                { start: 22 * 60 - 5, end: 23 * 60 + 5, period: "Durante a Pausa", time: "22:00-23:00" },
                { start: 23 * 60, end: 23 * 60 + 20, period: "Após a Pausa", time: "23:05-23:15" }
            ];

            for (let timeRange of criticalTimes) {
                if (currentTimeMinutes >= timeRange.start && currentTimeMinutes <= timeRange.end) {
                    showTradingNotification(timeRange.period, timeRange.time);
                    break;
                }
            }
        }

        // Função para mostrar notificação de trading
        function showTradingNotification(period, timeRange) {
            const notification = $('#trading-notification');
            const title = $('#notification-title');
            const message = $('#notification-message');

            title.text(`⚠️ ${period} de Trading`);
            message.text(`Horário crítico: ${timeRange} (Portugal) - Spreads podem estar afetados`);

            if (!notification.is(':visible')) {
                notification.show().addClass('trading-notification-pulse');
            }
        }

        // Função para fechar notificação
        function closeTradingNotification() {
            $('#trading-notification').hide().removeClass('trading-notification-pulse');
        }

        // Função para carregar dados dos spreads
        function loadSpreadsData() {
            const tbody = $('#spreads-body');
            tbody.empty();

            spreadsData.forEach(function(item) {
                let rowClass = '';
                let badgeClass = '';
                let statusIcon = '';

                switch(item.observation) {
                    case 'Muito afetado':
                        rowClass = 'spread-very-affected';
                        badgeClass = 'obs-muito-afetado';
                        statusIcon = '<i class="fas fa-exclamation-triangle text-danger"></i>';
                        break;
                    case 'Moderadamente afetado':
                        rowClass = 'spread-moderately-affected';
                        badgeClass = 'obs-moderadamente-afetado';
                        statusIcon = '<i class="fas fa-exclamation-circle text-warning"></i>';
                        break;
                    case 'Pouco afetado':
                        rowClass = 'spread-little-affected';
                        badgeClass = 'obs-pouco-afetado';
                        statusIcon = '<i class="fas fa-check-circle text-success"></i>';
                        break;
                }

                const row = `
                    <tr class="${rowClass}">
                        <td class="currency-pair">${item.currencyPair}</td>
                        <td class="spread-value">${item.spreadBeforePause_2150_2159}</td>
                        <td class="spread-value">${item.spreadDuringPause_2200_2300}</td>
                        <td class="spread-value">${item.spreadAfterPause_2305_2315}</td>
                        <td class="text-center">${statusIcon}</td>
                    </tr>
                `;
                tbody.append(row);
            });
        }

        // Função para carregar as notícias econômicas
        function loadEconomicNews() {
            $.ajax({
                url: '{{ route("economic.news") }}',
                type: 'GET',
                dataType: 'json',
                success: function(data) {
                    updateEconomicNewsTable(data);
                },
                complete: function() {
                    // Atualiza a cada 60 segundos
                    setTimeout(loadEconomicNews, 60000);
                }
            });
        }

        // Função para atualizar a tabela de notícias econômicas
        function updateEconomicNewsTable(data) {
            const container = $('#economic-news-body');
            container.empty();

            // Obter a hora atual do Brasil para comparação
            const now = new Date();
            const brazilTime = new Date(now.getTime());

            // Brasil é UTC-3
            brazilTime.setHours(now.getUTCHours() - 3);

            // Hora atual em minutos desde meia-noite
            const currentHour = brazilTime.getHours();
            const currentMinute = brazilTime.getMinutes();
            const currentTimeInMinutes = currentHour * 60 + currentMinute;

            // Calcular o limite de tempo (1h antes da hora atual)
            const timeWindowInMinutes = 60; // 1h em minutos
            const cutoffTimeInMinutes = currentTimeInMinutes - timeWindowInMinutes;

            // Filtrar apenas eventos futuros ou recentes (dentro da janela de tempo)
            const filteredData = data.filter(function(item) {
                const eventTimeParts = item.hora.split(':');
                if (eventTimeParts.length === 2) {
                    const eventHour = parseInt(eventTimeParts[0]);
                    const eventMinute = parseInt(eventTimeParts[1]);
                    const eventTimeInMinutes = eventHour * 60 + eventMinute;

                    // Lidar com eventos que cruzam a meia-noite
                    if (cutoffTimeInMinutes < 0) {
                        // Se o limite for negativo (ex: 23:00 - 1h30min = -30min)
                        return eventTimeInMinutes >= (cutoffTimeInMinutes + 24 * 60) || eventTimeInMinutes <= currentTimeInMinutes;
                    } else {
                        // Caso normal
                        return eventTimeInMinutes >= cutoffTimeInMinutes;
                    }
                }
                return true; // Se não conseguir analisar a hora, inclui o evento
            });

            // Ordenar eventos por hora
            filteredData.sort(function(a, b) {
                const aTimeParts = a.hora.split(':');
                const bTimeParts = b.hora.split(':');

                if (aTimeParts.length === 2 && bTimeParts.length === 2) {
                    const aHour = parseInt(aTimeParts[0]);
                    const aMinute = parseInt(aTimeParts[1]);
                    const bHour = parseInt(bTimeParts[0]);
                    const bMinute = parseInt(bTimeParts[1]);

                    const aTimeInMinutes = aHour * 60 + aMinute;
                    const bTimeInMinutes = bHour * 60 + bMinute;

                    return aTimeInMinutes - bTimeInMinutes;
                }

                return 0;
            });

            // Mostrar apenas eventos filtrados
            filteredData.forEach(function(item) {
                // Determina a classe de impacto baseada no número de touros
                let impactClass = '';
                if (item.impacto === 3) {
                    impactClass = 'negative-value'; // Vermelho para alto impacto
                } else if (item.impacto === 2) {
                    impactClass = 'valor-zero'; // Amarelo para médio impacto
                } else {
                    impactClass = 'positive-value'; // Verde para baixo impacto
                }

                // Cria o indicador de impacto visual
                let impactIndicator = '';
                for (let i = 0; i < item.impacto; i++) {
                    impactIndicator += '<i class="fas fa-circle"></i> ';
                }

                // Verifica se o evento está próximo da hora atual (±15 minutos)
                let isNearCurrentTime = false;
                const eventTimeParts = item.hora.split(':');
                if (eventTimeParts.length === 2) {
                    const eventHour = parseInt(eventTimeParts[0]);
                    const eventMinute = parseInt(eventTimeParts[1]);
                    const eventTimeInMinutes = eventHour * 60 + eventMinute;

                    // Diferença em minutos entre o evento e a hora atual
                    const timeDifference = Math.abs(eventTimeInMinutes - currentTimeInMinutes);

                    // Se a diferença for menor ou igual a 15 minutos, destaca o evento
                    if (timeDifference <= 15) {
                        isNearCurrentTime = true;
                        console.log(`Evento próximo: ${item.evento} - Hora: ${item.hora} - Diferença: ${timeDifference} minutos`);
                    }
                }

                // Cria a linha da tabela com classe especial para eventos próximos
                let rowHtml = '';
                if (isNearCurrentTime) {
                    rowHtml = `
                    <tr class="event-near-time">
                        <td>${item.hora}</td>
                        <td>${item.moeda}</td>
                        <td class="${impactClass}">${impactIndicator}</td>
                        <td>${item.evento}</td>
                    </tr>
                    `;
                } else {
                    rowHtml = `
                    <tr>
                        <td>${item.hora}</td>
                        <td>${item.moeda}</td>
                        <td class="${impactClass}">${impactIndicator}</td>
                        <td>${item.evento}</td>
                    </tr>
                    `;
                }

                container.append(rowHtml);
            });
        }

        // Função para simular dados das tabelas (já que não temos conexão com BD)
        function loadMockData() {
            // Dashboard simplificado - sem tabelas de dados
            console.log('Dashboard carregado com dados dos spreads');
        }

        // Inicializar quando a página carregar
        $(document).ready(function() {
            updateClocks();
            loadSpreadsData();
            loadEconomicNews();
            loadMockData();

            // Tornar a notificação global
            window.closeTradingNotification = closeTradingNotification;
        });
    </script>
</body>
</html>
