<!DOCTYPE html>
<html lang="pt-BR">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Dashboard de Ativos</title>
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/css/bootstrap.min.css" rel="stylesheet" />
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.2.0/css/all.min.css" rel="stylesheet" />
    <style>
        body {
            background-color: #212529;
            color: white;
            font-size: 0.8rem;
            /* Reduzido de 0.9rem */
        }

        .content {
            padding: 15px;
            /* Reduzido de 20px */
        }

        .card {
            background-color: #2c3034;
            border: none;
            border-radius: 8px;
            margin-bottom: 15px;
            /* Reduzido de 20px */
        }

        .card-body {
            padding: 15px;
            /* Reduzido de 20px */
        }

        .card-title {
            font-size: 0.8rem;
            /* Título reduzido */
        }

        .statistic-value {
            font-size: 20px;
            /* Reduzido de 24px */
            font-weight: bold;
        }

        /* Garantir que todos os valores estatísticos tenham o mesmo tamanho */
        .card .statistic-value {
            font-size: 20px !important;
        }

        /* Tamanho específico para os valores de martingale */
        .card .d-flex .statistic-value {
            font-size: 16px !important;
        }

        .trend-up {
            color: #28a745;
        }

        .trend-down {
            color: #dc3545;
        }

        .table {
            color: white;
            font-size: 0.75rem;
            /* Reduzido de 0.85rem */
        }

        .positive-value {
            color: #1db954;
        }

        .negative-value {
            color: #f45b5b;
        }

        .valor-zero {
            color: #ffc107;
        }

        .robo-info {
            color: #626262;
            font-size: 0.65em;
            /* Reduzido de 0.7em */
            display: block;
        }

        .data-info {
            color: #626262;
            font-size: 0.65em;
            /* Reduzido de 0.7em */
            display: block;
        }

        .news-item {
            background-color: #2c3034;
            border-radius: 8px;
            margin-bottom: 10px;
            /* Reduzido de 15px */
            /*padding: 10px;*/
            /* Reduzido de 15px */
            display: flex;
            align-items: flex-start;
            font-size: 0.75rem;
            /* Reduzido de 0.85rem */
        }

        .news-image {
            width: 80px;
            /* Reduzido de 100px */
            height: 60px;
            /* Reduzido de 80px */
            object-fit: cover;
            border-radius: 4px;
            margin-right: 10px;
            /* Reduzido de 15px */
        }

        .news-content {
            flex: 1;
        }

        .news-title {
            font-weight: 400;
            margin-bottom: 3px;
            /* Reduzido de 5px */
            font-size: 0.9rem;
            /* Reduzido de 1rem */
        }

        .news-author {
            color: #6c757d;
            font-size: 0.7rem;
            /* Reduzido de 0.75rem */
            margin-bottom: 3px;
            /* Reduzido de 5px */
        }

        .news-summary {
            font-size: 0.75rem;
            /* Reduzido de 0.85rem */
            color: #adb5bd;
            margin-bottom: 3px;
            /* Reduzido de 5px */
        }

        .news-section-title {
            font-size: 0.85rem;
            /* Reduzido de 1.3rem */
            margin-bottom: 15px;
            /* Reduzido de 20px */
            padding-bottom: 8px;
            /* Reduzido de 10px */
            border-bottom: 1px solid #454d55;
        }

        /* As cores para positive-value, negative-value e valor-zero são definidas em outras regras */

        .statistic-label {
            font-size: 0.8rem;
            color: #adb5bd;
            margin-right: 5px;
        }

        .martingale-label {
            font-size: 0.8rem;
            color: #adb5bd;
            font-weight: bold;
            margin-bottom: 5px;
        }

        /* Estilos para os backgrounds dos martingales */
        /* Para o card de estatísticas */
        .card .martingale-0 {
            background-color: rgba(29, 185, 84, 0.7); /* Verde como os valores positivos */
            border-radius: 5px;
            padding: 5px 10px;
            color: white;
        }

        .card .martingale-1 {
            background-color: rgba(255, 153, 0, 0.7); /* Amarelo mais alaranjado */
            border-radius: 5px;
            padding: 5px 10px;
            color: white;
        }

        .card .martingale-2 {
            background-color: rgba(244, 91, 91, 0.7); /* Vermelho como os valores negativos */
            border-radius: 5px;
            padding: 5px 10px;
            color: white;
        }

        /* Para as linhas das tabelas */
        tr.martingale-0 {
            background-color: rgba(29, 185, 84, 0.15) !important; /* Verde mais sutil */
        }

        tr.martingale-1 {
            background-color: rgba(255, 153, 0, 0.15) !important; /* Amarelo alaranjado mais sutil */
        }

        tr.martingale-2 {
            background-color: rgba(244, 91, 91, 0.15) !important; /* Vermelho mais sutil */
        }

        /* Hover nas linhas da tabela */
        tr.martingale-0:hover {
            background-color: rgba(29, 185, 84, 0.25) !important;
        }

        tr.martingale-1:hover {
            background-color: rgba(255, 153, 0, 0.25) !important;
        }

        tr.martingale-2:hover {
            background-color: rgba(244, 91, 91, 0.25) !important;
        }

        /* Estilos para alerta de valores negativos */
        #alert-negative {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px;
            border-radius: 6px;
            z-index: 1000;
            font-weight: bold;
            display: none;
            animation: blink 1s infinite;
        }

        /* Animação para o badge de status ativo */
        .badge-blink {
            animation: blink 1s infinite;
        }

        /* Estilos para o alerta de candles */
        .candles-alert {
            min-width: 100px;
            text-align: left;
            font-weight: bold;
        }

        .blink-green {
            animation: blinkGreenText 1s infinite;
            color: #1db954;
        }

        .blink-red {
            animation: blinkRedText 1s infinite;
            color: #f45b5b;
        }

        @keyframes blinkGreenText {
            0% { opacity: 0.7; }
            50% { opacity: 1; }
            100% { opacity: 0.7; }
        }

        @keyframes blinkRedText {
            0% { opacity: 0.7; }
            50% { opacity: 1; }
            100% { opacity: 0.7; }
        }

        /* Alerta amarelo para um valor negativo */
        #alert-negative.yellow-alert {
            background-color: #ff9900; /* Amarelo alaranjado */
            color: #000;
            box-shadow: 0 0 10px rgba(255, 153, 0, 0.8);
        }

        /* Alerta vermelho para dois ou mais valores negativos consecutivos */
        #alert-negative.red-alert {
            background-color: #dc3545; /* Vermelho */
            color: #fff;
            box-shadow: 0 0 10px rgba(220, 53, 69, 0.8);
        }

        @keyframes blink {
            0% {
                opacity: 1;
            }

            50% {
                opacity: 0.4;
            }

            100% {
                opacity: 1;
            }
        }

        /* Estilos para os relógios digitais */
        .clocks-container {
            position: absolute;
            top: 15px;
            right: 20px;
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
            max-width: 400px;
            justify-content: flex-end;
        }

        .digital-clock {
            display: flex;
            align-items: center;
            font-size: 0.9rem;
            color: #fff;
            margin-bottom: 5px;
        }

        .digital-clock img {
            width: 20px;
            height: 14px;
            margin-right: 5px;
        }

        /* Estilo para o container de vídeo ao vivo */
        .live-video-container {
            margin-bottom: 4px;
            border-radius: 6px;
            overflow: hidden;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .live-video-title {
            background-color: #2c3034;
            color: #fff;
            padding: 5px 8px;
            font-size: 0.7rem;
            font-weight: bold;
            display: flex;
            align-items: center;
        }

        .live-indicator {
            display: inline-block;
            width: 6px;
            height: 6px;
            background-color: #dc3545;
            border-radius: 50%;
            margin-right: 5px;
            animation: pulse 1.5s infinite;
        }

        @keyframes pulse {
            0% {
                opacity: 1;
            }

            50% {
                opacity: 0.5;
            }

            100% {
                opacity: 1;
            }
        }

        .video-responsive {
            position: relative;
            padding-bottom: 56.25%;
            /* Proporção 16:9 padrão para preencher todo o espaço */
            height: 0;
            overflow: hidden;
        }

        .video-responsive iframe {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            border: 0;
        }

        /* Ajustes para os vídeos lado a lado */
        .videos-row {
            margin-right: -2px;
            margin-left: -2px;
        }

        .videos-row .col-md-6 {
            padding-right: 2px;
            padding-left: 2px;
        }

        /* Estilo para a tabela de notícias econômicas */
        #economic-news-table td {
            white-space: normal;
            word-wrap: break-word;
        }

        #economic-news-table th:nth-child(4),
        #economic-news-table td:nth-child(4) {
            max-width: 150px;
            font-size: 0.7rem;
            color: #9ea5a5;
            word-break: break-word;
        }

        /* Estilo para as bolinhas de impacto */
        #economic-news-table .fa-circle {
            font-size: 0.5rem;
            margin-right: 2px;
        }

        /* Estilo para eventos próximos da hora atual */
        #economic-news-table tr.event-near-time,
        #economic-news-table tr.event-near-time td {
            background-color: #1f5305 !important;
        }

        /* Estilo para registros da última hora - IMPORTANTE: alta especificidade */
        table tr.last-hour-row,
        table tr.last-hour-row td,
        #ativos-table-1 tr.last-hour-row,
        #ativos-table-1 tr.last-hour-row td,
        #ativos-table-2 tr.last-hour-row,
        #ativos-table-2 tr.last-hour-row td {
            background-color: #343434 !important;
            border: 1px solid #212529 !important;
            padding: 4px 5px !important;
            margin: 0 !important;
            line-height: 1.3 !important;
            height: auto !important;
        }

        /* Ajuste para o conteúdo dentro das células da última hora */
        table tr.last-hour-row td span,
        #ativos-table-1 tr.last-hour-row td span,
        #ativos-table-2 tr.last-hour-row td span {
            padding: 2px !important;
            margin: 0 !important;
        }

        /* Ajuste específico para a coluna de martingale nas linhas da última hora */
        table tr.last-hour-row td:nth-child(2),
        #ativos-table-1 tr.last-hour-row td:nth-child(2),
        #ativos-table-2 tr.last-hour-row td:nth-child(2) {
            padding: 4px 8px !important;
        }

        /* Ajuste para as caixas coloridas de martingale nas linhas da última hora */
        table tr.last-hour-row td:nth-child(2) span,
        #ativos-table-1 tr.last-hour-row td:nth-child(2) span,
        #ativos-table-2 tr.last-hour-row td:nth-child(2) span {
            padding: 3px 8px !important;
            display: inline-block !important;
        }

        /* Estilo para o indicador de período crítico (bolinha vermelha) */
        .critical-period-indicator {
            display: inline-block;
            width: 10px;
            height: 10px;
            background-color: #f45b5b;
            border-radius: 50%;
            margin-right: 8px;
        }

        /* Estilo para o indicador de período favorável (bolinha verde) */
        .favorable-period-indicator {
            display: inline-block;
            width: 10px;
            height: 10px;
            background-color: #1db954;
            border-radius: 50%;
            margin-right: 8px;
        }

        /* Estilo para os botões de contagem de registros */
        .record-count-btn {
            transition: all 0.2s ease;
        }

        .record-count-btn.active {
            font-weight: bold;
            box-shadow: 0 0 5px rgba(0, 123, 255, 0.5);
        }

        /* Estilos para evitar scroll na tabela */
        .no-scroll-table-container {
            overflow: hidden;
            width: 100%;
        }

        #negative-hours-table {
            font-size: 0.85em;
        }

        #negative-hours-table th,
        #negative-hours-table td {
            padding: 0.4rem 0.2rem;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        /* Estilo para a coluna de Greens M0 */
        #negative-hours-table td:nth-child(2) {
            color: #1db954 !important;
            font-weight: bold;
            text-align: center;
        }

        /* Estilo para a coluna de M1 */
        #negative-hours-table td:nth-child(3) {
            color: #ffc107 !important;
            font-weight: bold;
            text-align: center;
        }

        /* Estilo para a coluna de M2 */
        #negative-hours-table td:nth-child(4) {
            color: #f45b5b !important;
            font-weight: bold;
            text-align: center;
        }

        /* Estilo para a coluna de Negativos */
        #negative-hours-table td:nth-child(5) {
            color: #f45b5b !important;
            text-align: center;
        }

        /* Estilo para a coluna de Total */
        #negative-hours-table td:nth-child(6) {
            text-align: center;
        }

        /* Estilo para a coluna de Percentual */
        #negative-hours-table td:nth-child(7) {
            text-align: center;
        }

        /* Estilo para os cabeçalhos da tabela */
        #negative-hours-table th {
            text-align: center;
            font-size: 0.8em;
        }
    </style>
</head>

<body>
    <div class="container-fluid">
        <div class="row">
            <div class="col-12 content">
                <h4 class="mb-3">Dashboard Blade</h4>

                <!-- Relógios digitais Brasil e Portugal -->
                <div class="clocks-container">
                    <!-- Relógio Brasil -->
                    <div class="digital-clock">
                        <img src="https://flagcdn.com/br.svg" alt="Bandeira do Brasil">
                        <span id="brazil-time">00:00:00</span>
                    </div>

                    <!-- Relógio Portugal -->
                    <div class="digital-clock">
                        <img src="https://flagcdn.com/pt.svg" alt="Bandeira de Portugal">
                        <span id="portugal-time">00:00:00</span>
                    </div>

                    <!-- Relógio EUA (Nova York) -->
                    <div class="digital-clock">
                        <img src="https://flagcdn.com/us.svg" alt="Bandeira dos EUA">
                        <span id="usa-time">00:00:00</span>
                    </div>

                    <!-- Relógio Reino Unido (Londres) -->
                    <div class="digital-clock">
                        <img src="https://flagcdn.com/gb.svg" alt="Bandeira do Reino Unido">
                        <span id="uk-time">00:00:00</span>
                    </div>
                </div>

                <!-- Alert para valores negativos -->
                <div id="alert-negative">
                    <i class="fas fa-exclamation-triangle"></i> <span id="alert-message">ALERTA: Valor negativo detectado!</span>
                </div>

                <!-- Statistics Cards - Todos em uma única linha -->
                <div class="row">
                <div class="col-md-2"></div>

                    <div class="col-md-2">
                        <div class="card">
                            <div class="card-body">
                                <div class="d-flex justify-content-around mb-2">
                                    <div class="text-center">
                                        <div class="martingale-label">Registros</div>
                                        <div class="statistic-value" id="total-ativos">-</div>
                                    </div>
                                    <div class="text-center">
                                        <div class="martingale-label">Greens</div>
                                        <div class="statistic-value positive-value" id="greens-count">-</div>
                                    </div>
                                    <div class="text-center">
                                        <div class="martingale-label">Reds</div>
                                        <div class="statistic-value negative-value" id="reds-count">-</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-2">
                        <div class="card">
                            <div class="card-body">
                                <div class="d-flex justify-content-around mb-2">
                                    <div class="text-center">
                                        <div class="martingale-label">Greens - Ult.Hora</div>
                                        <div class="statistic-value positive-value" id="last-hour-greens">-</div>
                                    </div>
                                    <div class="text-center">
                                        <div class="martingale-label">Reds - Ult.Hora</div>
                                        <div class="statistic-value negative-value" id="last-hour-reds">-</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-2">
                        <div class="card">
                            <div class="card-body">
                                <div class="d-flex justify-content-around mb-2">
                                    <div class="text-center">
                                        <div class="martingale-label">M2+ - Ult.Hora</div>
                                        <div class="statistic-value positive-value" id="last-hour-m2-positive">-</div>
                                    </div>
                                    <div class="text-center">
                                        <div class="martingale-label">M2- - Ult.Hora</div>
                                        <div class="statistic-value negative-value" id="last-hour-m2-negative">-</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-2">
                        <div class="card">
                            <div class="card-body">
                                <div class="d-flex justify-content-around mb-2">
                                    <div class="text-center">
                                        <div class="martingale-label">M2 Positivos</div>
                                        <div class="statistic-value positive-value" id="martingale-twos-positive">-</div>
                                    </div>
                                    <div class="text-center">
                                        <div class="martingale-label">M2 Negativos</div>
                                        <div class="statistic-value negative-value" id="martingale-twos-negative">-</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-2">
                        <div class="card">
                            <div class="card-body">
                                <div class="d-flex justify-content-around mb-2">
                                    <div class="text-center">
                                        <div class="martingale-label">M0</div>
                                        <div class="statistic-value positive-value" id="martingale-zeros">-</div>
                                    </div>
                                    <div class="text-center">
                                        <div class="martingale-label">M1</div>
                                        <div class="statistic-value" style="color: #ffc107;" id="martingale-ones">-</div>
                                    </div>
                                    <div class="text-center">
                                        <div class="martingale-label">M2</div>
                                        <div class="statistic-value negative-value" id="martingale-twos">-</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>


                <!-- Layout principal: 3 tabelas + coluna de notícias -->
                <div class="row">
                    <!-- Tabelas de ativos (ocupando 9 colunas) -->
                    <div class="col-md-9">
                        <div class="row">

                            <div class="col-md-2"></div>

                            <!-- Tabela de Análise de Horários Negativos -->
                            <div class="col-md-4">
                                <div class="card mt-3">
                                    <div class="card-body">
                                        <h4 class="card-title mb-3" id="negative-hours-title">
                                            Análise de Horários Negativos
                                            <span id="loading-indicator" class="ms-2">
                                                <i class="fas fa-spinner fa-spin"></i> Carregando registros...
                                            </span>
                                            <small id="data-info" style="display: none; color: #007bff; font-size: 0.8em; margin-left: 10px; font-weight: bold;"></small>
                                        </h4>
                                        <div class="mb-3 d-flex align-items-center" style="gap: 8px;">
                                            <div style="display: flex; gap: 8px;">
                                                <button type="button" class="btn btn-sm btn-outline-info record-count-btn" data-count="200" style="font-size: 0.7em; padding: 2px 8px;">200</button>
                                                <button type="button" class="btn btn-sm btn-info record-count-btn active" data-count="1000" style="font-size: 0.7em; padding: 2px 8px;">1000</button>
                                                <button type="button" class="btn btn-sm btn-outline-info record-count-btn" data-count="2000" style="font-size: 0.7em; padding: 2px 8px;">2000</button>
                                                <button type="button" class="btn btn-sm btn-outline-info record-count-btn" data-count="3000" style="font-size: 0.7em; padding: 2px 8px;">3000</button>
                                            </div>
                                            <div id="yesterday-total-orders" style="font-size: 0.75em; color: #6c757d; margin-left: 10px;"></div>
                                        </div>
                                        <div class="no-scroll-table-container">
                                            <table class="table table-dark table-hover table-sm" id="negative-hours-table" style="table-layout: fixed; width: 100%; margin-bottom: 0;">
                                                <thead>
                                                    <tr>
                                                        <th style="width: 30%; text-align: left; padding-left: 10px;">Período</th>
                                                        <th style="width: 10%;">M0</th>
                                                        <th style="width: 10%;">M1</th>
                                                        <th style="width: 10%;">M2</th>
                                                        <th style="width: 13%;">Reds</th>
                                                        <th style="width: 13%;">Total</th>
                                                        <th style="width: 14%;">%</th>
                                                    </tr>
                                                </thead>
                                                <tbody id="negative-hours-body">
                                                    <tr>
                                                        <td style="font-weight: bold; text-align: left; padding-left: 10px;">Manhã (06-11h)</td>
                                                        <td id="morning-m0-green">-</td>
                                                        <td id="morning-m1">-</td>
                                                        <td id="morning-m2">-</td>
                                                        <td id="morning-negative">-</td>
                                                        <td id="morning-total">-</td>
                                                        <td id="morning-percent">-</td>
                                                    </tr>
                                                    <tr>
                                                        <td style="font-weight: bold; text-align: left; padding-left: 10px;">Tarde (12-17h)</td>
                                                        <td id="afternoon-m0-green">-</td>
                                                        <td id="afternoon-m1">-</td>
                                                        <td id="afternoon-m2">-</td>
                                                        <td id="afternoon-negative">-</td>
                                                        <td id="afternoon-total">-</td>
                                                        <td id="afternoon-percent">-</td>
                                                    </tr>
                                                    <tr>
                                                        <td style="font-weight: bold; text-align: left; padding-left: 10px;">Noite (18-23h)</td>
                                                        <td id="evening-m0-green">-</td>
                                                        <td id="evening-m1">-</td>
                                                        <td id="evening-m2">-</td>
                                                        <td id="evening-negative">-</td>
                                                        <td id="evening-total">-</td>
                                                        <td id="evening-percent">-</td>
                                                    </tr>
                                                    <tr>
                                                        <td style="font-weight: bold; text-align: left; padding-left: 10px;">Madrugada (00-05h)</td>
                                                        <td id="night-m0-green">-</td>
                                                        <td id="night-m1">-</td>
                                                        <td id="night-m2">-</td>
                                                        <td id="night-negative">-</td>
                                                        <td id="night-total">-</td>
                                                        <td id="night-percent">-</td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-3">
                                 <!-- Configurações dos Robôs e Notificações (lado a lado) -->
                                    <div class="row mt-3">
                                        <!-- Robô - Padrão23 -->
                                        <div class="col-md-6">
                                            <div class="card h-100">
                                                <div class="card-body">
                                                    <h4 class="card-title mb-3" style="font-size: 0.95em;">
                                                        Robô - Padrão23
                                                        <span id="countdown-timer" style="color: #1db954; margin-left: 5px; font-size: 1.3em; font-weight: bold; display: none;"></span>
                                                    </h4>
                                                    <div class="mt-2">
                                                        <div class="form-check form-switch mb-2">
                                                            <input class="form-check-input" type="checkbox" id="toggleStart23" data-current-status="0">
                                                            <label class="form-check-label text-light" for="toggleStart23">Ativar </label>
                                                        </div>
                                                        <div class="text-center">
                                                            <span id="start23-status" class="badge bg-danger" style="width: 100%;">Desativado</span>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Notificações -->
                                        <div class="col-md-6">
                                            <div class="card h-100">
                                                <div class="card-body">
                                                    <h4 class="card-title mb-3" style="font-size: 0.95em;">
                                                        Notificações
                                                        <span id="notification-status-indicator" style="color: #1db954; margin-left: 5px; font-size: 0.8em; font-weight: bold; display: none;"></span>
                                                    </h4>
                                                    <div class="mt-2">
                                                        <div class="form-check form-switch mb-2">
                                                            <input class="form-check-input" type="checkbox" id="toggleNotifications" data-current-status="0">
                                                            <label class="form-check-label text-light" for="toggleNotifications">Ativar </label>
                                                        </div>
                                                        <div class="text-center">
                                                            <span id="notifications-status" class="badge bg-danger" style="width: 100%;">Desativado</span>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Tabela de Últimos 50 Candles -->
                                    <div class="row mb-3 mt-3">
                                        <div class="col-md-12">
                                            <div class="card">
                                                <div class="card-body">
                                                    <h4 class="card-title mb-3">
                                                        Contagem 100 Candles
                                                        <span id="candles-trend" style="margin-left: 10px; font-size: 0.8em;"></span>
                                                    </h4>
                                                    <div class="table-responsive">
                                                        <table class="table table-dark table-hover table-sm" id="last-candles-table">
                                                            <thead>
                                                                <tr>
                                                                    <th>Hora</th>
                                                                    <th>G</th>
                                                                    <th>R</th>
                                                                    <th>D</th>
                                                                </tr>
                                                            </thead>
                                                            <tbody id="last-candles-body">
                                                                <!-- Os dados serão carregados via JavaScript -->
                                                            </tbody>
                                                        </table>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>


                            </div>

                            <!-- Tabela 1 -->
                            <div class="col-md-3">
                                <div class="card mt-3">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between align-items-center mb-2">
                                            <div id="candles-alert" class="candles-alert"></div>
                                            <small class="text-muted"><span id="candles-timeframe">-</span> | <span id="candles-total">-</span> velas</small>
                                        </div>
                                        <div class="d-flex justify-content-around">
                                            <div class="text-center">
                                                <div class="martingale-label">Candles Greens</div>
                                                <div class="d-flex align-items-center justify-content-center">
                                                    <div class="statistic-value positive-value" id="green-candles-count">-</div>
                                                    <small class="text-muted ms-1" id="green-candles-percent">-%</small>
                                                </div>
                                            </div>
                                            <div class="text-center">
                                                <div class="martingale-label">Candles Reds</div>
                                                <div class="d-flex align-items-center justify-content-center">
                                                    <div class="statistic-value negative-value" id="red-candles-count">-</div>
                                                    <small class="text-muted ms-1" id="red-candles-percent">-%</small>
                                                </div>
                                            </div>
                                            <div class="text-center">
                                                <div class="martingale-label">Candles Doji</div>
                                                <div class="d-flex align-items-center justify-content-center">
                                                    <div class="statistic-value" id="doji-candles-count">-</div>
                                                    <small class="text-muted ms-1" id="doji-candles-percent">-%</small>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="card mt-3">
                                    <div class="card-body">
                                        <h4 class="card-title mb-3" id="tabela1-titulo">Resultados</h4>
                                        <div class="table-responsive">
                                            <table class="table table-dark table-hover" id="ativos-table-1">
                                                <thead>
                                                    <tr>
                                                        <th>Data/Hora</th>
                                                        <th>Mart.</th>
                                                        <th>Valor</th>
                                                    </tr>
                                                </thead>
                                                <tbody id="ativos-body-1">
                                                    <!-- Os dados serão carregados via JavaScript -->
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>

                        </div>
                    </div>

                    <!-- Coluna de Notícias (ocupando 3 colunas) -->
                    <div class="col-md-3">
                        <!-- Vídeos ao vivo -->
                        <div class="card mt-3">
                            <div class="card-body p-1">
                                <div class="row">

                                    <!-- Vídeo ao vivo da Euronews -->
                                    <div class="col-12">
                                        <div class="live-video-container">
                                            <div class="live-video-title">
                                                <span class="live-indicator"></span> EURONEWS AO VIVO
                                            </div>
                                            <div class="video-responsive">
                                                <iframe
                                                    src="https://www.youtube.com/embed/XuZAl-ZPEcA?autoplay=1&mute=1"
                                                    frameborder="0"
                                                    allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                                                    allowfullscreen>
                                                </iframe>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Notícias Econômicas -->
                        <div class="card mt-3">
                            <div class="card-body">
                                <h4 class="news-section-title">Notícias Econômicas</h4>
                                <div class="table-responsive">
                                    <table class="table table-dark table-hover" id="economic-news-table">
                                        <thead>
                                            <tr>
                                                <th>Hora</th>
                                                <th>Moeda</th>
                                                <th>Impacto</th>
                                                <th>Evento</th>
                                            </tr>
                                        </thead>
                                        <tbody id="economic-news-body">
                                            <!-- Os dados serão carregados via JavaScript -->
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>

                        <!-- Últimas Notícias -->
                        <div class="card mt-3">
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-12">
                                        <h4 class="news-section-title">Últimas Notícias</h4>
                                        <div id="news-container">
                                            <!-- As notícias serão carregadas via JavaScript -->
                                        </div>
                                    </div>
                                </div>

                            </div>
                        </div>
                    </div>
                </div>
            </div> <!-- fim da col-12 -->
        </div> <!-- fim da row -->
    </div> <!-- fim do container -->

    <!-- Arquivo de áudio para o alerta -->
    <audio id="alert-sound" src="https://assets.mixkit.co/sfx/preview/mixkit-alert-alarm-1005.mp3"></audio>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Função para atualizar os relógios digitais
        function updateClocks() {
            // Obter data e hora atual
            const now = new Date();

            // Configurar relógio do Brasil (UTC-3)
            const brazilTime = new Date(now.getTime());
            brazilTime.setHours(now.getUTCHours() - 3);

            // Configurar relógio de Portugal (UTC+1 no horário padrão, UTC+2 no horário de verão)
            const portugalTime = new Date(now.getTime());
            const currentMonth = now.getUTCMonth() + 1; // getMonth() retorna 0-11

            // Verificar se estamos no horário de verão (aproximadamente de março a outubro)
            const isDST = currentMonth >= 3 && currentMonth <= 10;

            // Horário de Portugal (UTC+0 no horário padrão, UTC+1 no horário de verão)
            if (isDST) {
                portugalTime.setHours(now.getUTCHours() + 1); // Horário de verão (UTC+1)
            } else {
                portugalTime.setHours(now.getUTCHours()); // Horário padrão (UTC+0)
            }

            // Configurar relógio dos EUA (Nova York) (UTC-5 no horário padrão, UTC-4 no horário de verão)
            const usaTime = new Date(now.getTime());
            if (isDST) {
                usaTime.setHours(now.getUTCHours() - 4); // Horário de verão (UTC-4)
            } else {
                usaTime.setHours(now.getUTCHours() - 5); // Horário padrão (UTC-5)
            }

            // Configurar relógio do Reino Unido (Londres) (UTC+0 no horário padrão, UTC+1 no horário de verão)
            const ukTime = new Date(now.getTime());
            if (isDST) {
                ukTime.setHours(now.getUTCHours() + 1); // Horário de verão (UTC+1)
            } else {
                ukTime.setHours(now.getUTCHours()); // Horário padrão (UTC+0)
            }

            // Formatar horas, minutos e segundos com zero à esquerda
            function formatTime(date) {
                const hours = date.getHours() < 10 ? '0' + date.getHours() : date.getHours();
                const minutes = date.getMinutes() < 10 ? '0' + date.getMinutes() : date.getMinutes();
                const seconds = date.getSeconds() < 10 ? '0' + date.getSeconds() : date.getSeconds();
                return hours + ':' + minutes + ':' + seconds;
            }

            // Atualizar relógios
            $('#brazil-time').text(formatTime(brazilTime));
            $('#portugal-time').text(formatTime(portugalTime));
            $('#usa-time').text(formatTime(usaTime));
            $('#uk-time').text(formatTime(ukTime));

            // Atualizar a cada segundo (1000 ms) para mostrar os segundos atualizados
            setTimeout(updateClocks, 1000);
        }

        // Variável global para armazenar a quantidade atual de registros
        let currentRecordCount = 1000;

        // Função para carregar uma quantidade específica de registros
        function loadCustomCountData(count) {
            // Mostrar indicador de carregamento
            $('#loading-indicator').show();
            $('#data-info').hide();

            // Atualizar a variável global
            currentRecordCount = count;

            // Atualizar a aparência dos botões
            $('.record-count-btn').removeClass('active').removeClass('btn-info').addClass('btn-outline-info');
            $(`.record-count-btn[data-count="${count}"]`).removeClass('btn-outline-info').addClass('btn-info active');

            $.ajax({
                url: `/ativos/custom/${count}`,
                type: 'GET',
                dataType: 'json',
                success: function(data) {
                    console.log(`Dados carregados: ${data.length} registros`);
                    updateDashboard(data);
                    checkConsecutiveNegatives(data);

                    // Atualizar informações sobre os dados carregados
                    const timestamp = new Date().toLocaleTimeString();
                    $('#data-info').text(`${count} registros analisados às ${timestamp}`).show();
                },
                error: function(xhr, status, error) {
                    console.error(`Erro ao carregar ${count} registros:`, error);
                    $('#data-info').text(`Erro ao carregar ${count} registros. Tente novamente.`).show();
                },
                complete: function() {
                    // Esconder indicador de carregamento
                    $('#loading-indicator').hide();
                }
            });
        }

        // Função para carregar os dados mais recentes (usando a quantidade atual de registros)
        function loadLatestData() {
            $.ajax({
                url: `/ativos/custom/${currentRecordCount}`,
                type: 'GET',
                dataType: 'json',
                success: function(data) {
                    console.log(`Dados carregados: ${data.length} registros`);
                    updateDashboard(data);
                    checkConsecutiveNegatives(data);

                    // Atualizar informações sobre os dados carregados
                    const timestamp = new Date().toLocaleTimeString();
                    $('#data-info').text(`${currentRecordCount} registros analisados às ${timestamp}`).show();
                },
                error: function(xhr, status, error) {
                    console.error('Erro ao carregar dados:', error);
                },
                complete: function() {
                    // Esconder indicador de carregamento após a primeira carga
                    $('#loading-indicator').hide();

                    // Atualiza a cada 5 segundos
                    setTimeout(loadLatestData, 5000);
                }
            });
        }

        // Função para carregar os dados de contagem de velas
        function loadCandleStats() {
            $.ajax({
                url: '{{ route("candles.latest") }}',
                type: 'GET',
                dataType: 'json',
                success: function(data) {
                    if (data && data.last_json) {
                        try {
                            // Converter a string JSON para objeto
                            const candleData = typeof data.last_json === 'string'
                                ? JSON.parse(data.last_json)
                                : data.last_json;

                            console.log('Dados de velas carregados:', candleData);

                            // Atualizar o quadro com os dados
                            updateCandleStats(candleData);
                        } catch (e) {
                            console.error('Erro ao processar dados de velas:', e);
                        }
                    } else {
                        console.warn('Nenhum dado de velas disponível');
                    }
                },
                error: function(xhr, status, error) {
                    console.error('Erro ao carregar dados de velas:', error);
                },
                complete: function() {
                    // Atualiza a cada 5 segundos
                    setTimeout(loadCandleStats, 5000);
                }
            });
        }

        // Função para atualizar o quadro de estatísticas de velas
        function updateCandleStats(data) {
            if (data && data.contagem && data.porcentagem) {
                // Atualizar contagens
                const greenCount = data.contagem.green || 0;
                const redCount = data.contagem.red || 0;
                const dojiCount = data.contagem.doji || 0;

                $('#green-candles-count').text(greenCount);
                $('#red-candles-count').text(redCount);
                $('#doji-candles-count').text(dojiCount);

                // Atualizar porcentagens
                $('#green-candles-percent').text(data.porcentagem.green ? `${data.porcentagem.green}%` : '0%');
                $('#red-candles-percent').text(data.porcentagem.red ? `${data.porcentagem.red}%` : '0%');
                $('#doji-candles-percent').text(data.porcentagem.doji ? `${data.porcentagem.doji}%` : '0%');

                // Atualizar informações adicionais
                $('#candles-timeframe').text(data.timeframe ? `${data.timeframe}m` : '');
                $('#candles-total').text(data.total_velas || 0);

                // Verificar a diferença entre candles greens e reds
                checkCandleDifference(greenCount, redCount);

                // Verificar a tendência com base na diferença percentual
                checkCandleTrend(data.porcentagem.green, data.porcentagem.red);
            }
        }

        // Função para verificar a tendência com base na diferença percentual entre candles verdes e vermelhos
        function checkCandleTrend(greenPercent, redPercent) {
            // Converter para números
            const greenPct = parseFloat(greenPercent) || 0;
            const redPct = parseFloat(redPercent) || 0;

            // Calcular a diferença absoluta entre as porcentagens
            const percentDifference = Math.abs(greenPct - redPct);

            // Limiar para determinar a tendência (70% de diferença)
            // TENDENCIA CANDLES: Altere o valor abaixo para ajustar o limiar de diferença percentual
            const trendThreshold = 30;

            const $trendElement = $('#candles-trend');

            // Se a diferença for maior que o limiar, mostrar a tendência
            if (percentDifference > trendThreshold) {
                if (greenPct > redPct) {
                    // Tendência de alta (mais candles verdes)
                    $trendElement.text('ALTA');
                    $trendElement.css('color', '#1db954'); // Verde
                } else {
                    // Tendência de baixa (mais candles vermelhos)
                    $trendElement.text('BAIXA');
                    $trendElement.css('color', '#f45b5b'); // Vermelho
                }
                $trendElement.show();
            } else {
                // Se a diferença não for significativa, não mostrar tendência
                $trendElement.text('');
                $trendElement.hide();
            }
        }


        // Função para verificar a diferença entre candles greens e reds
        function checkCandleDifference(greenCount, redCount) {
            const difference = Math.abs(greenCount - redCount);
            const $alertElement = $('#candles-alert');

            // Se a diferença for de 1 ou mais, mostrar o alerta (temporariamente para teste)
            if (difference >= 1) {
                if (greenCount < redCount) {
                    // Mais candles vermelhas, alerta verde (indicando que faltam candles verdes)
                    $alertElement.removeClass('blink-red').addClass('blink-green');
                    $alertElement.text(`Faltam ${difference} greens`);
                } else {
                    // Mais candles verdes, alerta vermelho (indicando que faltam candles vermelhas)
                    $alertElement.removeClass('blink-green').addClass('blink-red');
                    $alertElement.text(`Faltam ${difference} reds`);
                }
                $alertElement.show();
            } else {
                // Se a diferença for 0, esconder o alerta
                $alertElement.removeClass('blink-green blink-red').text('').hide();
            }
        }

        // Função para verificar valores negativos consecutivos
        function checkConsecutiveNegatives(data) {
            // Se não houver dados, esconde o alerta
            if (data.length === 0) {
                hideNegativeAlert();
                return;
            }

            // Verifica o valor mais recente
            const ultimoValor = parseFloat(data[0].value);

            // Se o valor mais recente for positivo, esconde o alerta
            if (ultimoValor > 0) {
                hideNegativeAlert();
                return;
            }

            // Se o valor mais recente for negativo
            if (ultimoValor < 0) {
                // Verifica se há pelo menos 2 valores na lista
                if (data.length > 1) {
                    const penultimoValor = parseFloat(data[1].value);

                    // Se o penúltimo valor também for negativo, mostra alerta vermelho
                    if (penultimoValor < 0) {
                        showRedAlert();
                        return;
                    }
                }

                // Se chegou aqui, apenas o último valor é negativo, mostra alerta amarelo
                showYellowAlert();
                return;
            }

            // Se chegou aqui, o último valor não é negativo
            hideNegativeAlert();
        }

        // Função para mostrar o alerta amarelo (um valor negativo)
        function showYellowAlert() {
            $('#alert-negative').removeClass('red-alert').addClass('yellow-alert');
            $('#alert-message').text('ALERTA: Valor negativo detectado!');
            $('#alert-negative').fadeIn();
            // Toca o som de alerta
            document.getElementById('alert-sound').play();
        }

        // Função para mostrar o alerta vermelho (dois ou mais valores negativos consecutivos)
        function showRedAlert() {
            $('#alert-negative').removeClass('yellow-alert').addClass('red-alert');
            $('#alert-message').text('ALERTA: Valores negativos consecutivos detectados!');
            $('#alert-negative').fadeIn();
            // Toca o som de alerta
            document.getElementById('alert-sound').play();
        }

        // Função para esconder o alerta de valores negativos
        function hideNegativeAlert() {
            $('#alert-negative').fadeOut();
        }

        // Função para carregar as notícias mais recentes
        function loadLatestNews() {
            $.ajax({
                url: '{{ route("news.latest") }}',
                type: 'GET',
                dataType: 'json',
                success: function(data) {
                    updateNews(data);
                },
                complete: function() {
                    // Atualiza a cada 10 segundos
                    setTimeout(loadLatestNews, 10000);
                }
            });
        }

        // Função para formatar a data no padrão DD.MM.YYYY
        function formatDate(dateStr) {
            const parts = dateStr.split('-');
            if (parts.length === 3) {
                return `${parts[2]}.${parts[1]}.${parts[0]}`;
            }
            return dateStr;
        }

        // Função para analisar os horários com mais valores negativos (apenas do dia de ontem)
        function analyzeNegativeHours(data) {
            // Contadores para cada período do dia
            let morningNegative = 0;
            let morningTotal = 0;
            let morningM0Green = 0;
            let morningM1 = 0;
            let morningM2 = 0;

            let afternoonNegative = 0;
            let afternoonTotal = 0;
            let afternoonM0Green = 0;
            let afternoonM1 = 0;
            let afternoonM2 = 0;

            let eveningNegative = 0;
            let eveningTotal = 0;
            let eveningM0Green = 0;
            let eveningM1 = 0;
            let eveningM2 = 0;

            let nightNegative = 0;
            let nightTotal = 0;
            let nightM0Green = 0;
            let nightM1 = 0;
            let nightM2 = 0;

            // Obter a data de ontem no formato YYYY-MM-DD
            const today = new Date();
            const yesterday = new Date(today);
            yesterday.setDate(yesterday.getDate() - 1);
            const yesterdayStr = yesterday.toISOString().split('T')[0]; // Formato YYYY-MM-DD

            // Atualizar o título da tabela para mostrar que é apenas do dia de ontem
            const yesterdayFormatted = `${yesterday.getDate().toString().padStart(2, '0')}/${(yesterday.getMonth() + 1).toString().padStart(2, '0')}/${yesterday.getFullYear()}`;
            $('#negative-hours-title').html(`Análise de Horários Negativos <small style="font-size: 0.7em; color: #6c757d;">(${yesterdayFormatted})</small>`);

            console.log(`Filtrando dados apenas para o dia de ontem: ${yesterdayStr}`);

            // Verificar o formato das datas nos primeiros 5 registros para depuração
            if (data.length > 0) {
                console.log('Amostra de formatos de data nos registros:');
                for (let i = 0; i < Math.min(5, data.length); i++) {
                    console.log(`Registro ${i+1}: data=${data[i].data}, hora=${data[i].hora}`);
                }
            }

            // Filtrar apenas os registros de ontem
            const yesterdayData = data.filter(ativo => {
                try {
                    // Verificar se o registro tem data
                    if (!ativo.data) {
                        console.log('Registro sem data:', ativo);
                        return false;
                    }

                    // Extrair a data do registro
                    let dataRegistro;

                    // Se a data estiver no formato DD/MM/YYYY
                    if (ativo.data.includes('/')) {
                        const partes = ativo.data.split('/');
                        if (partes.length === 3) {
                            // Criar um objeto Date com a data do registro (formato DD/MM/YYYY)
                            dataRegistro = new Date(`${partes[2]}-${partes[1]}-${partes[0]}`);
                        } else {
                            console.log('Formato de data inválido (com /)', ativo.data);
                            return false;
                        }
                    }
                    // Se a data estiver no formato YYYY-MM-DD
                    else if (ativo.data.includes('-')) {
                        dataRegistro = new Date(ativo.data);
                    }
                    // Outro formato desconhecido
                    else {
                        console.log('Formato de data desconhecido:', ativo.data);
                        return false;
                    }

                    // Verificar se a data é válida
                    if (isNaN(dataRegistro.getTime())) {
                        console.log('Data inválida após conversão:', ativo.data);
                        return false;
                    }

                    // Comparar apenas o dia, mês e ano (ignorando horas)
                    const dataRegistroStr = dataRegistro.toISOString().split('T')[0];
                    const isYesterday = dataRegistroStr === yesterdayStr;

                    // Para depuração
                    if (isYesterday) {
                        console.log('Encontrado registro de ontem:', ativo.data, ativo.hora);
                    }

                    return isYesterday;
                } catch (error) {
                    console.error('Erro ao processar data:', error, ativo);
                    return false;
                }
            });

            console.log(`Total de registros: ${data.length}, Registros de ontem: ${yesterdayData.length}`);

            // Se não houver dados de ontem, usar todos os dados disponíveis
            if (yesterdayData.length === 0) {
                console.log('Nenhum registro encontrado para ontem. Usando todos os dados disponíveis.');
                // Atualizar o título para indicar que estamos usando todos os dados
                $('#negative-hours-title').html(`Análise de Horários Negativos <small style="font-size: 0.7em; color: #6c757d;">(Todos os dados)</small>`);
                // Usar todos os dados
                analyzeAllData(data);
                return;
            }

            // Analisar cada registro de ontem
            yesterdayData.forEach(function(ativo) {
                try {
                    // Extrair a hora do registro
                    if (!ativo.hora) {
                        console.log('Registro sem hora:', ativo);
                        return; // Pular este registro
                    }

                    // Extrair a hora do formato HH:MM:SS ou HH:MM
                    let horaNum;
                    if (ativo.hora.includes(':')) {
                        horaNum = parseInt(ativo.hora.split(':')[0]);
                    } else {
                        horaNum = parseInt(ativo.hora);
                    }

                    // Verificar se a hora é válida
                    if (isNaN(horaNum) || horaNum < 0 || horaNum > 23) {
                        console.log('Hora inválida:', ativo.hora);
                        return; // Pular este registro
                    }

                    // Extrair valor e martingale
                    const valor = parseFloat(ativo.value);
                    const martingale = parseInt(ativo.martingale);

                    if (isNaN(valor) || isNaN(martingale)) {
                        console.log('Valor ou martingale inválido:', ativo);
                        return; // Pular este registro
                    }

                    console.log(`Processando registro: hora=${horaNum}, valor=${valor}, martingale=${martingale}`);

                    // Determinar o período do dia
                    let period;
                    if (horaNum >= 6 && horaNum < 12) {
                        period = 'morning';
                        morningTotal++;

                        // Contar negativos
                        if (valor < 0) {
                            morningNegative++;
                        }

                        // Contar por martingale
                        if (martingale === 0 && valor > 0) {
                            morningM0Green++;
                        } else if (martingale === 1) {
                            morningM1++;
                        } else if (martingale === 2) {
                            morningM2++;
                        }

                    } else if (horaNum >= 12 && horaNum < 18) {
                        period = 'afternoon';
                        afternoonTotal++;

                        // Contar negativos
                        if (valor < 0) {
                            afternoonNegative++;
                        }

                        // Contar por martingale
                        if (martingale === 0 && valor > 0) {
                            afternoonM0Green++;
                        } else if (martingale === 1) {
                            afternoonM1++;
                        } else if (martingale === 2) {
                            afternoonM2++;
                        }

                    } else if (horaNum >= 18 && horaNum < 24) {
                        period = 'evening';
                        eveningTotal++;

                        // Contar negativos
                        if (valor < 0) {
                            eveningNegative++;
                        }

                        // Contar por martingale
                        if (martingale === 0 && valor > 0) {
                            eveningM0Green++;
                        } else if (martingale === 1) {
                            eveningM1++;
                        } else if (martingale === 2) {
                            eveningM2++;
                        }

                    } else {
                        period = 'night';
                        nightTotal++;

                        // Contar negativos
                        if (valor < 0) {
                            nightNegative++;
                        }

                        // Contar por martingale
                        if (martingale === 0 && valor > 0) {
                            nightM0Green++;
                        } else if (martingale === 1) {
                            nightM1++;
                        } else if (martingale === 2) {
                            nightM2++;
                        }
                    }

                    // Log para depuração
                    console.log(`Período: ${period}, hora: ${horaNum}, valor: ${valor}, martingale: ${martingale}`);

                } catch (error) {
                    console.error('Erro ao processar registro:', error, ativo);
                }
            });

            // Log dos totais por período
            console.log(`Manhã: ${morningTotal}, Tarde: ${afternoonTotal}, Noite: ${eveningTotal}, Madrugada: ${nightTotal}`);

            // Calcular o total de ordens de ontem
            const totalYesterdayOrders = morningTotal + afternoonTotal + eveningTotal + nightTotal;

            // Atualizar o elemento que mostra o total de ordens
            $('#yesterday-total-orders').text(`Total de ontem: ${totalYesterdayOrders} ordens`);

            // Calcular percentuais
            const morningPercent = morningTotal > 0 ? ((morningNegative / morningTotal) * 100).toFixed(1) : '0.0';
            const afternoonPercent = afternoonTotal > 0 ? ((afternoonNegative / afternoonTotal) * 100).toFixed(1) : '0.0';
            const eveningPercent = eveningTotal > 0 ? ((eveningNegative / eveningTotal) * 100).toFixed(1) : '0.0';
            const nightPercent = nightTotal > 0 ? ((nightNegative / nightTotal) * 100).toFixed(1) : '0.0';

            // Atualizar a tabela
            $('#morning-negative').text(morningNegative);
            $('#morning-m0-green').text(morningM0Green);
            $('#morning-m1').text(morningM1);
            $('#morning-m2').text(morningM2);
            $('#morning-total').text(morningTotal);
            $('#morning-percent').text(morningPercent + '%');

            $('#afternoon-negative').text(afternoonNegative);
            $('#afternoon-m0-green').text(afternoonM0Green);
            $('#afternoon-m1').text(afternoonM1);
            $('#afternoon-m2').text(afternoonM2);
            $('#afternoon-total').text(afternoonTotal);
            $('#afternoon-percent').text(afternoonPercent + '%');

            $('#evening-negative').text(eveningNegative);
            $('#evening-m0-green').text(eveningM0Green);
            $('#evening-m1').text(eveningM1);
            $('#evening-m2').text(eveningM2);
            $('#evening-total').text(eveningTotal);
            $('#evening-percent').text(eveningPercent + '%');

            $('#night-negative').text(nightNegative);
            $('#night-m0-green').text(nightM0Green);
            $('#night-m1').text(nightM1);
            $('#night-m2').text(nightM2);
            $('#night-total').text(nightTotal);
            $('#night-percent').text(nightPercent + '%');

            // Identificar o período com maior percentual de negativos
            const periods = [
                { id: 'morning', name: 'Manhã (06-11h)', percent: parseFloat(morningPercent), m0Green: morningM0Green },
                { id: 'afternoon', name: 'Tarde (12-17h)', percent: parseFloat(afternoonPercent), m0Green: afternoonM0Green },
                { id: 'evening', name: 'Noite (18-23h)', percent: parseFloat(eveningPercent), m0Green: eveningM0Green },
                { id: 'night', name: 'Madrugada (00-05h)', percent: parseFloat(nightPercent), m0Green: nightM0Green }
            ];

            // Ordenar períodos por percentual de negativos (do maior para o menor)
            const criticalPeriods = [...periods].sort((a, b) => b.percent - a.percent);

            // Ordenar períodos por número de Greens M0 (do maior para o menor)
            const favorablePeriods = [...periods].sort((a, b) => b.m0Green - a.m0Green);

            // Remover os indicadores de todas as linhas
            $('#negative-hours-body tr td:first-child').each(function() {
                const text = $(this).text();
                $(this).html(text
                    .replace('<span class="critical-period-indicator"></span>', '')
                    .replace('<span class="favorable-period-indicator"></span>', '')
                );
            });

            // Adicionar o indicador (bolinha vermelha) ao período com maior percentual de negativos
            if (criticalPeriods[0].percent > 0) {
                const criticalPeriodId = criticalPeriods[0].id;
                const $cell = $(`#${criticalPeriodId}-negative`).closest('tr').find('td:first-child');
                let currentHtml = $cell.html();
                $cell.html(`<span class="critical-period-indicator"></span>${currentHtml}`);
            }

            // Adicionar o indicador (bolinha verde) ao período com maior número de Greens M0
            if (favorablePeriods[0].m0Green > 0) {
                const favorablePeriodId = favorablePeriods[0].id;

                // Não adicionar a bolinha verde se for o mesmo período que já tem a bolinha vermelha
                if (favorablePeriodId !== criticalPeriods[0].id) {
                    const $cell = $(`#${favorablePeriodId}-negative`).closest('tr').find('td:first-child');
                    let currentHtml = $cell.html();
                    $cell.html(`<span class="favorable-period-indicator"></span>${currentHtml}`);
                } else {
                    // Se for o mesmo período, adicionar a bolinha verde após a vermelha
                    const $cell = $(`#${favorablePeriodId}-negative`).closest('tr').find('td:first-child');
                    let currentHtml = $cell.html();
                    // Substituir a bolinha vermelha por ambas as bolinhas
                    currentHtml = currentHtml.replace(
                        '<span class="critical-period-indicator"></span>',
                        '<span class="critical-period-indicator"></span><span class="favorable-period-indicator"></span>'
                    );
                    $cell.html(currentHtml);
                }
            }

            // Atualizar a mensagem de informação para incluir a data de ontem
            const dataInfo = $('#data-info').text();
            if (dataInfo) {
                $('#data-info').text(`${dataInfo} (Apenas dados de ${yesterdayFormatted})`);
            }
        }

        // Função para analisar todos os dados disponíveis (quando não há dados de ontem)
        function analyzeAllData(data) {
            // Contadores para cada período do dia
            let morningNegative = 0;
            let morningTotal = 0;
            let morningM0Green = 0;
            let morningM1 = 0;
            let morningM2 = 0;

            let afternoonNegative = 0;
            let afternoonTotal = 0;
            let afternoonM0Green = 0;
            let afternoonM1 = 0;
            let afternoonM2 = 0;

            let eveningNegative = 0;
            let eveningTotal = 0;
            let eveningM0Green = 0;
            let eveningM1 = 0;
            let eveningM2 = 0;

            let nightNegative = 0;
            let nightTotal = 0;
            let nightM0Green = 0;
            let nightM1 = 0;
            let nightM2 = 0;

            // Analisar cada registro
            data.forEach(function(ativo) {
                try {
                    // Extrair a hora do registro
                    if (!ativo.hora) {
                        console.log('Registro sem hora:', ativo);
                        return; // Pular este registro
                    }

                    // Extrair a hora do formato HH:MM:SS ou HH:MM
                    let horaNum;
                    if (ativo.hora.includes(':')) {
                        horaNum = parseInt(ativo.hora.split(':')[0]);
                    } else {
                        horaNum = parseInt(ativo.hora);
                    }

                    // Verificar se a hora é válida
                    if (isNaN(horaNum) || horaNum < 0 || horaNum > 23) {
                        console.log('Hora inválida:', ativo.hora);
                        return; // Pular este registro
                    }

                    // Extrair valor e martingale
                    const valor = parseFloat(ativo.value);
                    const martingale = parseInt(ativo.martingale);

                    if (isNaN(valor) || isNaN(martingale)) {
                        console.log('Valor ou martingale inválido:', ativo);
                        return; // Pular este registro
                    }

                    // Determinar o período do dia
                    let period;
                    if (horaNum >= 6 && horaNum < 12) {
                        period = 'morning';
                        morningTotal++;

                        // Contar negativos
                        if (valor < 0) {
                            morningNegative++;
                        }

                        // Contar por martingale
                        if (martingale === 0 && valor > 0) {
                            morningM0Green++;
                        } else if (martingale === 1) {
                            morningM1++;
                        } else if (martingale === 2) {
                            morningM2++;
                        }

                    } else if (horaNum >= 12 && horaNum < 18) {
                        period = 'afternoon';
                        afternoonTotal++;

                        // Contar negativos
                        if (valor < 0) {
                            afternoonNegative++;
                        }

                        // Contar por martingale
                        if (martingale === 0 && valor > 0) {
                            afternoonM0Green++;
                        } else if (martingale === 1) {
                            afternoonM1++;
                        } else if (martingale === 2) {
                            afternoonM2++;
                        }

                    } else if (horaNum >= 18 && horaNum < 24) {
                        period = 'evening';
                        eveningTotal++;

                        // Contar negativos
                        if (valor < 0) {
                            eveningNegative++;
                        }

                        // Contar por martingale
                        if (martingale === 0 && valor > 0) {
                            eveningM0Green++;
                        } else if (martingale === 1) {
                            eveningM1++;
                        } else if (martingale === 2) {
                            eveningM2++;
                        }

                    } else {
                        period = 'night';
                        nightTotal++;

                        // Contar negativos
                        if (valor < 0) {
                            nightNegative++;
                        }

                        // Contar por martingale
                        if (martingale === 0 && valor > 0) {
                            nightM0Green++;
                        } else if (martingale === 1) {
                            nightM1++;
                        } else if (martingale === 2) {
                            nightM2++;
                        }
                    }
                } catch (error) {
                    console.error('Erro ao processar registro (todos os dados):', error, ativo);
                }
            });

            // Log dos totais por período
            console.log(`[Todos os dados] Manhã: ${morningTotal}, Tarde: ${afternoonTotal}, Noite: ${eveningTotal}, Madrugada: ${nightTotal}`);

            // Calcular o total de ordens
            const totalOrders = morningTotal + afternoonTotal + eveningTotal + nightTotal;

            // Atualizar o elemento que mostra o total de ordens
            $('#yesterday-total-orders').text(`Total: ${totalOrders} ordens`);

            // Calcular percentuais
            const morningPercent = morningTotal > 0 ? ((morningNegative / morningTotal) * 100).toFixed(1) : '0.0';
            const afternoonPercent = afternoonTotal > 0 ? ((afternoonNegative / afternoonTotal) * 100).toFixed(1) : '0.0';
            const eveningPercent = eveningTotal > 0 ? ((eveningNegative / eveningTotal) * 100).toFixed(1) : '0.0';
            const nightPercent = nightTotal > 0 ? ((nightNegative / nightTotal) * 100).toFixed(1) : '0.0';

            // Atualizar a tabela
            $('#morning-negative').text(morningNegative);
            $('#morning-m0-green').text(morningM0Green);
            $('#morning-m1').text(morningM1);
            $('#morning-m2').text(morningM2);
            $('#morning-total').text(morningTotal);
            $('#morning-percent').text(morningPercent + '%');

            $('#afternoon-negative').text(afternoonNegative);
            $('#afternoon-m0-green').text(afternoonM0Green);
            $('#afternoon-m1').text(afternoonM1);
            $('#afternoon-m2').text(afternoonM2);
            $('#afternoon-total').text(afternoonTotal);
            $('#afternoon-percent').text(afternoonPercent + '%');

            $('#evening-negative').text(eveningNegative);
            $('#evening-m0-green').text(eveningM0Green);
            $('#evening-m1').text(eveningM1);
            $('#evening-m2').text(eveningM2);
            $('#evening-total').text(eveningTotal);
            $('#evening-percent').text(eveningPercent + '%');

            $('#night-negative').text(nightNegative);
            $('#night-m0-green').text(nightM0Green);
            $('#night-m1').text(nightM1);
            $('#night-m2').text(nightM2);
            $('#night-total').text(nightTotal);
            $('#night-percent').text(nightPercent + '%');

            // Identificar o período com maior percentual de negativos
            const periods = [
                { id: 'morning', name: 'Manhã (06-11h)', percent: parseFloat(morningPercent), m0Green: morningM0Green },
                { id: 'afternoon', name: 'Tarde (12-17h)', percent: parseFloat(afternoonPercent), m0Green: afternoonM0Green },
                { id: 'evening', name: 'Noite (18-23h)', percent: parseFloat(eveningPercent), m0Green: eveningM0Green },
                { id: 'night', name: 'Madrugada (00-05h)', percent: parseFloat(nightPercent), m0Green: nightM0Green }
            ];

            // Ordenar períodos por percentual de negativos (do maior para o menor)
            const criticalPeriods = [...periods].sort((a, b) => b.percent - a.percent);

            // Ordenar períodos por número de Greens M0 (do maior para o menor)
            const favorablePeriods = [...periods].sort((a, b) => b.m0Green - a.m0Green);

            // Remover os indicadores de todas as linhas
            $('#negative-hours-body tr td:first-child').each(function() {
                const text = $(this).text();
                $(this).html(text
                    .replace('<span class="critical-period-indicator"></span>', '')
                    .replace('<span class="favorable-period-indicator"></span>', '')
                );
            });

            // Adicionar o indicador (bolinha vermelha) ao período com maior percentual de negativos
            if (criticalPeriods[0].percent > 0) {
                const criticalPeriodId = criticalPeriods[0].id;
                const $cell = $(`#${criticalPeriodId}-negative`).closest('tr').find('td:first-child');
                let currentHtml = $cell.html();
                $cell.html(`<span class="critical-period-indicator"></span>${currentHtml}`);
            }

            // Adicionar o indicador (bolinha verde) ao período com maior número de Greens M0
            if (favorablePeriods[0].m0Green > 0) {
                const favorablePeriodId = favorablePeriods[0].id;

                // Não adicionar a bolinha verde se for o mesmo período que já tem a bolinha vermelha
                if (favorablePeriodId !== criticalPeriods[0].id) {
                    const $cell = $(`#${favorablePeriodId}-negative`).closest('tr').find('td:first-child');
                    let currentHtml = $cell.html();
                    $cell.html(`<span class="favorable-period-indicator"></span>${currentHtml}`);
                } else {
                    // Se for o mesmo período, adicionar a bolinha verde após a vermelha
                    const $cell = $(`#${favorablePeriodId}-negative`).closest('tr').find('td:first-child');
                    let currentHtml = $cell.html();
                    // Substituir a bolinha vermelha por ambas as bolinhas
                    currentHtml = currentHtml.replace(
                        '<span class="critical-period-indicator"></span>',
                        '<span class="critical-period-indicator"></span><span class="favorable-period-indicator"></span>'
                    );
                    $cell.html(currentHtml);
                }
            }

            // Atualizar a mensagem de informação para indicar que estamos usando todos os dados
            const dataInfo = $('#data-info').text();
            if (dataInfo) {
                $('#data-info').text(`${dataInfo} (Todos os dados disponíveis)`);
            }
        }

        // Função para atualizar o dashboard com os novos dados
        function updateDashboard(data) {
            // Limpa a tabela antes de adicionar novos dados
            $('#ativos-body-1').empty();

            // Configura os contadores de estatísticas
            $('#total-ativos').text(data.length);

            // Contadores para estatísticas
            let greensCount = 0;
            let redsCount = 0;
            let martingaleZeros = 0;
            let martingaleOnes = 0;
            let martingaleTwos = 0;
            let martingaleTwosPositive = 0;
            let martingaleTwosNegative = 0;

            // Contadores para estatísticas da última hora
            let lastHourGreens = 0;
            let lastHourReds = 0;
            let lastHourM2Positive = 0;
            let lastHourM2Negative = 0;

            // Analisar os horários com mais valores negativos
            analyzeNegativeHours(data);

            // Vamos imprimir o primeiro registro para entender o formato dos dados
            if (data.length > 0) {
                console.log('Exemplo de registro:', JSON.stringify(data[0]));
            }

            // Primeiro, ordenamos os dados por data/hora (mais recentes primeiro)
            data.sort((a, b) => {
                // Converter datas para timestamps para comparação
                const aTimestamp = new Date(`${a.data} ${a.hora}`).getTime();
                const bTimestamp = new Date(`${b.data} ${b.hora}`).getTime();
                return bTimestamp - aTimestamp; // Ordem decrescente (mais recentes primeiro)
            });

            console.log('Dados ordenados por data/hora (primeiros 5):', data.slice(0, 5).map(d => `${d.id}: ${d.data} ${d.hora}`));

            // Criar um conjunto com os IDs dos registros da "última hora"
            const lastHourIds = new Set();

            // Verificar se temos pelo menos um registro
            if (data.length > 0) {
                // Obter o registro mais recente como referência
                const referenceRecord = data[0];
                const referenceTime = new Date(`${referenceRecord.data} ${referenceRecord.hora}`).getTime();

                console.log(`Registro de referência: ID=${referenceRecord.id}, Data=${referenceRecord.data}, Hora=${referenceRecord.hora}`);

                // Calcular o limite de tempo (1 hora antes)
                const oneHourInMs = 60 * 60 * 1000; // 1 hora em milissegundos
                const cutoffTime = referenceTime - oneHourInMs;

                console.log(`Limite de tempo (1 hora antes): ${new Date(cutoffTime).toLocaleString()}`);

                // Marcar todos os registros dentro do intervalo de 1 hora
                for (const record of data) {
                    const recordTime = new Date(`${record.data} ${record.hora}`).getTime();

                    // Se o registro está dentro do intervalo de 1 hora a partir do mais recente
                    if (recordTime >= cutoffTime && recordTime <= referenceTime) {
                        lastHourIds.add(record.id);
                        console.log(`Registro dentro da última hora: ID=${record.id}, Data=${record.data}, Hora=${record.hora}`);
                    } else if (recordTime < cutoffTime) {
                        // Se já passamos do limite de 1 hora, podemos parar (os dados estão ordenados)
                        console.log(`Registro fora do intervalo de 1 hora: ID=${record.id}, Data=${record.data}, Hora=${record.hora}`);
                        break;
                    }
                }
            }

            // Armazenar globalmente
            window.lastHourIds = lastHourIds;
            console.log(`Total de ${lastHourIds.size} registros marcados como "última hora"`);

            // Resetar contadores
            lastHourGreens = 0;
            lastHourReds = 0;
            lastHourM2Positive = 0;
            lastHourM2Negative = 0;

            // Usar todos os dados para a tabela 1
            const allData = data;

            // Função para popular uma tabela
            function populateTable(tableData, tableBodyId, tableIndex) {
                // Se temos dados, atualiza o título da tabela com o nome do ativo e robô
                if (tableData.length > 0) {
                    const firstItem = tableData[0];
                    $(`#tabela${tableIndex}-titulo`).text(`${firstItem.ativo} - ${firstItem.robo}`);
                }

                tableData.forEach(function(ativo) {
                    const valor = parseFloat(ativo.value);

                    // Contagem de greens e reds baseada no valor
                    if (valor > 0) {
                        greensCount++;
                    } else if (valor < 0) {
                        redsCount++;
                    }

                    // Contagem de martingales
                    const martingaleValue = parseFloat(ativo.martingale);

                    // Log para depuração
                    console.log(`Processando registro: martingale=${martingaleValue}, valor=${valor}`);

                    // Log do payload para depuração
                    if (ativo.payload) {
                        console.log(`Payload do registro ID=${ativo.id}:`, ativo.payload);
                    }

                    if (martingaleValue === 0) {
                        martingaleZeros++;
                    } else if (martingaleValue === 1) {
                        martingaleOnes++;
                    } else if (martingaleValue === 2) {
                        martingaleTwos++;

                        // Separar M2 positivos e negativos
                        if (valor > 0) {
                            martingaleTwosPositive++;
                            console.log(`M2 Positivo encontrado: valor=${valor}, martingale=${martingaleValue}, total=${martingaleTwosPositive}`);
                        } else if (valor < 0) {
                            martingaleTwosNegative++;
                            console.log(`M2 Negativo encontrado: valor=${valor}, martingale=${martingaleValue}, total=${martingaleTwosNegative}`);
                        }
                    }

                    // Variável para verificar se o registro é da última hora
                    let isLastHour = false;

                    // Verificar se o registro é da "última hora" (entre os 20 mais recentes)
                    if (ativo.id && window.lastHourIds && window.lastHourIds.has(ativo.id)) {
                        isLastHour = true; // Marcar como registro da "última hora"
                        console.log(`Registro da última hora encontrado: ID=${ativo.id}`);

                        // Contar greens e reds da última hora
                        if (valor > 0) {
                            lastHourGreens++;
                        } else if (valor < 0) {
                            lastHourReds++;
                        }

                        // Contar M2 positivos e negativos da última hora
                        if (martingaleValue === 2) {
                            if (valor > 0) {
                                lastHourM2Positive++;
                            } else if (valor < 0) {
                                lastHourM2Negative++;
                            }
                        }
                    }

                    // Determinar a classe de cor para o valor
                    let valueClass = '';
                    if (valor > 0) {
                        valueClass = 'positive-value';
                    } else if (valor < 0) {
                        valueClass = 'negative-value';
                    } else {
                        valueClass = 'valor-zero';
                    }

                    // Verificar o tipo de vela no payload
                    let candleArrow = '';
                    try {
                        if (ativo.payload) {
                            const payloadData = typeof ativo.payload === 'string'
                                ? JSON.parse(ativo.payload)
                                : ativo.payload;

                            if (payloadData.tipo_vela === 'green') {
                                // Setinha verde para cima
                                candleArrow = '<span style="color: #1db954; margin-left: 5px; font-size: 14px; vertical-align: middle;">&#9650;</span>';
                            } else if (payloadData.tipo_vela === 'red') {
                                // Setinha vermelha para baixo
                                candleArrow = '<span style="color: #f45b5b; margin-left: 5px; font-size: 14px; vertical-align: middle;">&#9660;</span>';
                            }
                        }
                    } catch (e) {
                        console.error('Erro ao processar payload:', e);
                    }

                    // Formatar data no padrão DD.MM.YYYY
                    const formattedDate = formatDate(ativo.data);

                    // Formatar data e hora (com a setinha depois da hora)
                    const dataHora = `<span>${ativo.hora}${candleArrow}</span><span class="data-info">${formattedDate}</span>`;

                    // Determinar a classe de martingale para a linha
                    let martingaleClass = '';
                    if (martingaleValue === 0) {
                        martingaleClass = 'martingale-0';
                    } else if (martingaleValue === 1) {
                        martingaleClass = 'martingale-1';
                    } else if (martingaleValue === 2) {
                        martingaleClass = 'martingale-2';
                    }

                    // Criar uma caixa colorida para o valor de martingale com cores mais intensas
                    let boxStyle = '';
                    if (martingaleValue === 0) {
                        boxStyle = 'background-color: rgba(40, 167, 69, 0.7); color: white;';
                    } else if (martingaleValue === 1) {
                        boxStyle = 'background-color: rgba(255, 153, 0, 0.7); color: white;';
                    } else if (martingaleValue === 2) {
                        boxStyle = 'background-color: rgba(220, 53, 69, 0.7); color: white;';
                    }

                    const martingaleBox = `<span style="padding: 2px 6px; border-radius: 3px; ${boxStyle}">${ativo.martingale}</span>`;

                    // Adicionar classe para registros da última hora
                    let rowClass = martingaleClass;
                    if (isLastHour) {
                        rowClass = `${martingaleClass} last-hour-row`;
                        console.log(`Linha da última hora: ${ativo.data} ${ativo.hora} - Valor: ${valor} - Martingale: ${martingaleValue} - Classe: ${rowClass}`);
                    }

                    // Criar a linha da tabela com a classe apropriada
                    let row = '';
                    if (isLastHour) {
                        // Linha da última hora com estilo inline para garantir que seja aplicado
                        row = `
            <tr class="${rowClass}" style="background-color: #343434 !important; border: 1px solid #212529 !important;">
              <td style="background-color: #343434 !important; padding: 4px 5px !important;">${dataHora}</td>
              <td style="background-color: #343434 !important; text-align: center; padding: 4px 8px !important;">${martingaleBox}</td>
              <td class="${valueClass}" style="background-color: #343434 !important; padding: 4px 5px !important;">${valor.toFixed(2)}</td>
            </tr>
          `;
                    } else {
                        // Linha normal
                        row = `
            <tr class="${rowClass}">
              <td>${dataHora}</td>
              <td style="text-align: center;">${martingaleBox}</td>
              <td class="${valueClass}">${valor.toFixed(2)}</td>
            </tr>
          `;
                    }

                    $(`#${tableBodyId}`).append(row);
                });
            }

            // Popula a tabela com todos os dados
            populateTable(allData, 'ativos-body-1', 1);

            // Log dos valores finais para depuração
            console.log('Estatísticas finais:');
            console.log(`Total de Greens: ${greensCount}`);
            console.log(`Total de Reds: ${redsCount}`);
            console.log(`Total de M0: ${martingaleZeros}`);
            console.log(`Total de M1: ${martingaleOnes}`);
            console.log(`Total de M2: ${martingaleTwos}`);
            console.log(`Total de M2 Positivos: ${martingaleTwosPositive}`);
            console.log(`Total de M2 Negativos: ${martingaleTwosNegative}`);

            // Atualiza os valores estatísticos
            $('#greens-count').text(greensCount);
            $('#reds-count').text(redsCount);
            $('#martingale-zeros').text(martingaleZeros);
            $('#martingale-ones').text(martingaleOnes);
            $('#martingale-twos').text(martingaleTwos);

            // Verificar se os elementos existem antes de atualizar
            const m2PositiveElement = $('#martingale-twos-positive');
            const m2NegativeElement = $('#martingale-twos-negative');

            if (m2PositiveElement.length) {
                console.log('Elemento M2 Positivo encontrado, atualizando para:', martingaleTwosPositive);
                m2PositiveElement.text(martingaleTwosPositive);
            } else {
                console.error('Elemento M2 Positivo não encontrado!');
            }

            if (m2NegativeElement.length) {
                console.log('Elemento M2 Negativo encontrado, atualizando para:', martingaleTwosNegative);
                m2NegativeElement.text(martingaleTwosNegative);
            } else {
                console.error('Elemento M2 Negativo não encontrado!');
            }

            // Atualiza as estatísticas da última hora
            $('#last-hour-greens').text(lastHourGreens);
            $('#last-hour-reds').text(lastHourReds);
            $('#last-hour-m2-positive').text(lastHourM2Positive);
            $('#last-hour-m2-negative').text(lastHourM2Negative);
        }

        // Função para atualizar o painel de notícias
        function updateNews(newsData) {
            const container = $('#news-container');
            container.empty();

            newsData.forEach(function(news) {
                const newsItem = `
          <div class="news-item">
            <img src="${news.image}" alt="Imagem da notícia" class="news-image">
            <div class="news-content">
              <div class="news-title">${news.title}</div>
              <div class="news-author">${news.author || 'Desconhecido'} - ${news.date}</div>
              <div class="news-summary">${news.summary}</div>
            </div>
          </div>
        `;
                container.append(newsItem);
            });
        }


        // Função para carregar as notícias econômicas
        function loadEconomicNews() {
            $.ajax({
                url: '{{ route("economic.news") }}',
                type: 'GET',
                dataType: 'json',
                success: function(data) {
                    updateEconomicNewsTable(data);
                },
                complete: function() {
                    // Atualiza a cada 15 segundos
                    setTimeout(loadEconomicNews, 60000);
                }
            });
        }

        // Função para atualizar a tabela de notícias econômicas
        function updateEconomicNewsTable(data) {
            const container = $('#economic-news-body');
            container.empty();

            // Obter a hora atual do Brasil para comparação
            const now = new Date();
            const brazilTime = new Date(now.getTime());

            // Brasil é UTC-3
            brazilTime.setHours(now.getUTCHours() - 3);

            // Hora atual em minutos desde meia-noite
            const currentHour = brazilTime.getHours();
            const currentMinute = brazilTime.getMinutes();
            const currentTimeInMinutes = currentHour * 60 + currentMinute;

            // Calcular o limite de tempo (1h antes da hora atual)
            const timeWindowInMinutes = 60; // 1h em minutos
            const cutoffTimeInMinutes = currentTimeInMinutes - timeWindowInMinutes;

            // Filtrar apenas eventos futuros ou recentes (dentro da janela de tempo)
            const filteredData = data.filter(function(item) {
                const eventTimeParts = item.hora.split(':');
                if (eventTimeParts.length === 2) {
                    const eventHour = parseInt(eventTimeParts[0]);
                    const eventMinute = parseInt(eventTimeParts[1]);
                    const eventTimeInMinutes = eventHour * 60 + eventMinute;

                    // Lidar com eventos que cruzam a meia-noite
                    if (cutoffTimeInMinutes < 0) {
                        // Se o limite for negativo (ex: 23:00 - 1h30min = -30min)
                        return eventTimeInMinutes >= (cutoffTimeInMinutes + 24 * 60) || eventTimeInMinutes <= currentTimeInMinutes;
                    } else {
                        // Caso normal
                        return eventTimeInMinutes >= cutoffTimeInMinutes;
                    }
                }
                return true; // Se não conseguir analisar a hora, inclui o evento
            });

            // Ordenar eventos por hora
            filteredData.sort(function(a, b) {
                const aTimeParts = a.hora.split(':');
                const bTimeParts = b.hora.split(':');

                if (aTimeParts.length === 2 && bTimeParts.length === 2) {
                    const aHour = parseInt(aTimeParts[0]);
                    const aMinute = parseInt(aTimeParts[1]);
                    const bHour = parseInt(bTimeParts[0]);
                    const bMinute = parseInt(bTimeParts[1]);

                    const aTimeInMinutes = aHour * 60 + aMinute;
                    const bTimeInMinutes = bHour * 60 + bMinute;

                    return aTimeInMinutes - bTimeInMinutes;
                }

                return 0;
            });

            // Mostrar apenas eventos filtrados
            filteredData.forEach(function(item) {
                // Determina a classe de impacto baseada no número de touros
                let impactClass = '';
                if (item.impacto === 3) {
                    impactClass = 'negative-value'; // Vermelho para alto impacto
                } else if (item.impacto === 2) {
                    impactClass = 'valor-zero'; // Amarelo para médio impacto
                } else {
                    impactClass = 'positive-value'; // Verde para baixo impacto
                }

                // Cria o indicador de impacto visual
                let impactIndicator = '';
                for (let i = 0; i < item.impacto; i++) {
                    impactIndicator += '<i class="fas fa-circle"></i> ';
                }

                // Verifica se o evento está próximo da hora atual (±15 minutos)
                let isNearCurrentTime = false;
                const eventTimeParts = item.hora.split(':');
                if (eventTimeParts.length === 2) {
                    const eventHour = parseInt(eventTimeParts[0]);
                    const eventMinute = parseInt(eventTimeParts[1]);
                    const eventTimeInMinutes = eventHour * 60 + eventMinute;

                    // Diferença em minutos entre o evento e a hora atual
                    const timeDifference = Math.abs(eventTimeInMinutes - currentTimeInMinutes);

                    // Se a diferença for menor ou igual a 15 minutos, destaca o evento
                    if (timeDifference <= 15) {
                        isNearCurrentTime = true;
                        console.log(`Evento próximo: ${item.evento} - Hora: ${item.hora} - Diferença: ${timeDifference} minutos`);
                    }
                }

                // Cria a linha da tabela com classe especial para eventos próximos
                let rowHtml = '';
                if (isNearCurrentTime) {
                    rowHtml = `
                    <tr class="event-near-time">
                        <td>${item.hora}</td>
                        <td>${item.moeda}</td>
                        <td class="${impactClass}">${impactIndicator}</td>
                        <td>${item.evento}</td>
                    </tr>
                    `;
                } else {
                    rowHtml = `
                    <tr>
                        <td>${item.hora}</td>
                        <td>${item.moeda}</td>
                        <td class="${impactClass}">${impactIndicator}</td>
                        <td>${item.evento}</td>
                    </tr>
                    `;
                }

                container.append(rowHtml);
            });
        }

        // Função para carregar o status atual do Start23
        function loadStart23Status() {
            console.log('Iniciando carregamento do status do Start23...');

            // URL completa para depuração
            const url = '{{ route("robots.get-start23-status") }}';
            console.log('URL da requisição:', url);

            $.ajax({
                url: url,
                type: 'GET',
                dataType: 'json',
                cache: false, // Evita cache do navegador
                beforeSend: function() {
                    console.log('Enviando requisição para obter status do Start23...');
                },
                success: function(response) {
                    console.log('Resposta recebida:', response);

                    if (response.success) {
                        console.log('Status do Start23 carregado com sucesso. Valor:', response.status);
                        // Converte para número para garantir
                        const statusValue = parseInt(response.status);
                        updateStart23Toggle(statusValue);
                    } else {
                        console.error('Erro ao carregar status do Start23:', response.message);
                    }
                },
                error: function(xhr, status, error) {
                    console.error('Erro na requisição para obter status do Start23');
                    console.error('Status:', status);
                    console.error('Erro:', error);
                    console.error('Resposta:', xhr.responseText);

                    // Em caso de erro, assume que está desativado por segurança
                    updateStart23Toggle(0);
                },
                complete: function() {
                    console.log('Requisição para obter status do Start23 finalizada');
                }
            });
        }

        // Função para atualizar a interface do toggle com base no status
        function updateStart23Toggle(status) {
            console.log('Atualizando interface para status:', status);

            // Garantir que status seja tratado como número
            status = parseInt(status);

            // Atualizar o atributo data-current-status
            $('#toggleStart23').attr('data-current-status', status);
            console.log('Atributo data-current-status atualizado para:', $('#toggleStart23').attr('data-current-status'));

            // Atualizar o estado do checkbox
            $('#toggleStart23').prop('checked', status === 1);
            console.log('Checkbox atualizado, checked:', $('#toggleStart23').prop('checked'));

            // Atualizar o texto e a cor do badge
            if (status === 1) {
                console.log('Atualizando badge para Ativo');
                $('#start23-status')
                    .removeClass('bg-danger')
                    .addClass('bg-success badge-blink')
                    .text('Ativo');
            } else {
                console.log('Atualizando badge para Desativado');
                $('#start23-status')
                    .removeClass('bg-success badge-blink')
                    .addClass('bg-danger')
                    .text('Desativado');
            }

            // Verificar estado final
            console.log('Estado final do toggle - data-current-status:', $('#toggleStart23').attr('data-current-status'));
            console.log('Estado final do toggle - checked:', $('#toggleStart23').prop('checked'));
            console.log('Estado final do badge - text:', $('#start23-status').text());
            console.log('Estado final do badge - classes:', $('#start23-status').attr('class'));
        }

        // Função para alternar o status do Start23
        function toggleStart23Status() {
            const currentStatus = $('#toggleStart23').attr('data-current-status');
            console.log('Alternando status do Start23. Status atual:', currentStatus);

            // URL completa para depuração
            const url = '{{ route("robots.toggle-start23") }}';
            console.log('URL da requisição:', url);

            $.ajax({
                url: url,
                type: 'POST',
                dataType: 'json',
                data: {
                    id: 1, // Assumindo que estamos trabalhando com o registro de ID 1
                    current_status: currentStatus,
                    _token: '{{ csrf_token() }}'
                },
                beforeSend: function() {
                    console.log('Enviando requisição para alternar status do Start23...');
                    console.log('Dados enviados:', {
                        id: 1,
                        current_status: currentStatus,
                        _token: '{{ csrf_token() }}'
                    });
                },
                success: function(response) {
                    console.log('Resposta recebida:', response);

                    if (response.success) {
                        console.log('Status alternado com sucesso. Novo status:', response.new_status);
                        updateStart23Toggle(response.new_status);

                        // Mostrar mensagem de sucesso
                        alert(response.message);
                    } else {
                        console.error('Erro ao atualizar o status:', response.message || 'Erro desconhecido');
                        alert('Erro ao atualizar o status.');
                    }
                },
                error: function(xhr, status, error) {
                    console.error('Erro na requisição para alternar status do Start23');
                    console.error('Status:', status);
                    console.error('Erro:', error);
                    console.error('Resposta:', xhr.responseText);

                    alert('Erro de comunicação com o servidor.');
                },
                complete: function() {
                    console.log('Requisição para alternar status do Start23 finalizada');
                }
            });
        }

        // Função para carregar os últimos 50 registros de candles
        function loadLast50Candles() {
            $.ajax({
                url: '{{ route("candles.last50") }}',
                type: 'GET',
                dataType: 'json',
                success: function(data) {
                    if (Array.isArray(data) && data.length > 0) {
                        updateCandlesTable(data);
                    } else {
                        console.warn('Nenhum dado de candles disponível para a tabela');
                    }
                },
                error: function(xhr, status, error) {
                    console.error('Erro ao carregar dados dos últimos 50 candles:', error);
                },
                complete: function() {
                    // Atualiza a cada 5 segundos
                    setTimeout(loadLast50Candles, 5000);
                }
            });
        }

        // Função para atualizar a tabela de candles
        function updateCandlesTable(data) {
            const $tableBody = $('#last-candles-body');
            $tableBody.empty();

            // Filtrar linhas com valores repetidos, mantendo apenas a mais antiga
            const filteredData = [];
            let lastGreen = null;
            let lastRed = null;
            let lastDoji = null;

            // Percorrer os dados do mais recente para o mais antigo
            for (let i = 0; i < data.length; i++) {
                const candle = data[i];
                const greenCount = candle.green || 0;
                const redCount = candle.red || 0;
                const dojiCount = candle.doji || 0;

                // Se os valores forem diferentes dos últimos registrados, adicionar à lista filtrada
                if (greenCount !== lastGreen || redCount !== lastRed || dojiCount !== lastDoji) {
                    filteredData.push(candle);

                    // Atualizar os últimos valores registrados
                    lastGreen = greenCount;
                    lastRed = redCount;
                    lastDoji = dojiCount;
                }
            }

            // Manter a ordem decrescente (do mais recente para o mais antigo)
            // Não precisamos inverter, pois os dados já vêm ordenados por id desc do controller

            // Renderizar as linhas filtradas
            filteredData.forEach(function(candle) {
                // Formatar apenas a hora (sem a data)
                let hora = 'N/A';
                if (candle.data_hora) {
                    const date = new Date(candle.data_hora.replace(' ', 'T'));
                    if (!isNaN(date.getTime())) {
                        hora = date.toLocaleString('pt-BR', {
                            hour: '2-digit',
                            minute: '2-digit'
                        });
                    }
                }

                // Verificar se a diferença entre greens e reds é superior a 7
                const greenCount = candle.green || 0;
                const redCount = candle.red || 0;
                const difference = Math.abs(greenCount - redCount);

                // Calcular a diferença com sinal (positivo para mais greens, negativo para mais reds)
                const signedDifference = greenCount - redCount;
                const diffText = signedDifference > 0 ? `+${signedDifference}` : `${signedDifference}`;

                // Definir a cor da hora com base na diferença
                let horaColor = '';
                if (Math.abs(signedDifference) >= 10) {
                    // Laranja se a diferença for >= 10
                    horaColor = '#fd7e14';
                } else if (difference > 7) {
                    // Amarelo se a diferença for > 7 e < 10
                    horaColor = '#ffc107';
                }

                // Definir a classe para a hora
                const horaClass = horaColor ? `style="color: ${horaColor};"` : '';

                // Cor da diferença sempre cinza
                const diffColor = '#6c757d';

                // Adicionar bolinha azul quando greens e reds forem iguais
                const equalIndicator = greenCount === redCount ?
                    '<span style="display: inline-block; width: 6px; height: 6px; background-color: #007bff; border-radius: 50%; margin-right: 4px;"></span>' :
                    '';

                // Criar a linha da tabela
                const row = `
                    <tr>
                        <td ${horaClass}>${equalIndicator}${hora} <small style="color: ${diffColor}; font-size: 80%;">${diffText}</small></td>
                        <td class="positive-value">${greenCount}</td>
                        <td class="negative-value">${redCount}</td>
                        <td>${candle.doji}</td>
                    </tr>
                `;

                $tableBody.append(row);
            });
        }

        // Função para verificar e iniciar o cronômetro quando necessário
        function checkAndStartCountdown() {
            // Obter a hora atual do Brasil
            const now = new Date();
            const brazilTime = new Date(now.getTime());
            brazilTime.setHours(now.getUTCHours() - 3);

            // Obter minutos e segundos atuais
            const currentMinute = brazilTime.getMinutes();
            const currentSecond = brazilTime.getSeconds();

            // Lista de minutos específicos para verificar
            const targetMinutes = [1, 6, 11, 15, 21, 26, 31, 36, 41, 46, 51, 56];

            // Encontrar o próximo minuto alvo
            let nextTargetMinute = null;
            let shouldShowCountdown = false;
            let secondsRemaining = 0;

            // Verificar se estamos a 60 segundos ou menos de um minuto alvo
            for (const targetMinute of targetMinutes) {
                if (currentMinute === targetMinute - 1 && currentSecond >= 0) {
                    // Estamos no minuto anterior a um minuto alvo
                    if (currentSecond >= 0) {
                        // Mostrar o contador apenas nos últimos 30 segundos
                        if (currentSecond >= 30) {
                            shouldShowCountdown = true;
                            secondsRemaining = 60 - currentSecond;
                            break;
                        }
                    }
                }
            }

            // Se devemos mostrar o contador
            if (shouldShowCountdown) {
                // Iniciar o cronômetro
                startCountdown(secondsRemaining);
            } else {
                // Esconder o cronômetro se não estamos próximos de um minuto alvo
                $('#countdown-timer').hide();
            }

            // Verificar novamente em 1 segundo
            setTimeout(checkAndStartCountdown, 1000);
        }

        // Variável global para controlar o estado do contador
        let countdownActive = false;
        let countdownInterval = null;

        // Função para iniciar o cronômetro decrescente
        function startCountdown(seconds) {
            const $countdownElement = $('#countdown-timer');

            // Se o contador já estiver ativo, não iniciar novamente
            if (countdownActive) {
                return;
            }

            // Marcar o contador como ativo
            countdownActive = true;

            // Limpar qualquer intervalo existente
            if (countdownInterval) {
                clearInterval(countdownInterval);
            }

            // Função para atualizar o contador
            function updateCounter() {
                // Se chegamos a zero ou menos, esconder o contador e parar
                if (seconds <= 0) {
                    $countdownElement.hide();
                    countdownActive = false;
                    clearInterval(countdownInterval);
                    return;
                }

                // Mostrar o contador
                $countdownElement.show();

                // Atualizar o texto
                $countdownElement.text(seconds);

                // Decrementar os segundos
                seconds--;
            }

            // Atualizar imediatamente
            updateCounter();

            // Configurar o intervalo para atualizar a cada segundo
            countdownInterval = setInterval(updateCounter, 1000);
        }



        // Função para carregar o status atual das notificações
        function loadNotificationsStatus() {
            console.log('Iniciando carregamento do status das notificações...');

            // URL completa para depuração
            const url = '{{ route("robots.get-start23-status") }}';
            console.log('URL da requisição para notificações:', url);

            $.ajax({
                url: url,
                type: 'GET',
                dataType: 'json',
                cache: false, // Evita cache do navegador
                beforeSend: function() {
                    console.log('Enviando requisição para obter status das notificações...');
                },
                success: function(response) {
                    console.log('Resposta recebida para notificações:', response);

                    if (response.success) {
                        console.log('Status das notificações carregado com sucesso. Valor:', response.notifications);
                        // Converte para número para garantir
                        const statusValue = parseInt(response.notifications || 0);
                        updateNotificationsToggle(statusValue);
                    } else {
                        console.error('Erro ao carregar status das notificações:', response.message);
                    }
                },
                error: function(xhr, status, error) {
                    console.error('Erro na requisição para obter status das notificações');
                    console.error('Status:', status);
                    console.error('Erro:', error);
                    console.error('Resposta:', xhr.responseText);

                    // Em caso de erro, assume que está desativado por segurança
                    updateNotificationsToggle(0);
                },
                complete: function() {
                    console.log('Requisição para obter status das notificações finalizada');
                }
            });
        }

        // Função para atualizar a interface do toggle de notificações com base no status
        function updateNotificationsToggle(status) {
            console.log('Atualizando interface de notificações para status:', status);

            // Garantir que status seja tratado como número
            status = parseInt(status);

            // Atualizar o atributo data-current-status
            $('#toggleNotifications').attr('data-current-status', status);
            console.log('Atributo data-current-status atualizado para:', $('#toggleNotifications').attr('data-current-status'));

            // Atualizar o estado do checkbox
            $('#toggleNotifications').prop('checked', status === 1);
            console.log('Checkbox de notificações atualizado, checked:', $('#toggleNotifications').prop('checked'));

            // Atualizar o texto e a cor do badge
            if (status === 1) {
                console.log('Atualizando badge para Ativo');
                $('#notifications-status')
                    .removeClass('bg-danger')
                    .addClass('bg-success badge-blink')
                    .text('Ativo');
            } else {
                console.log('Atualizando badge para Desativado');
                $('#notifications-status')
                    .removeClass('bg-success badge-blink')
                    .addClass('bg-danger')
                    .text('Desativado');
            }

            // Verificar estado final
            console.log('Estado final do toggle de notificações - data-current-status:', $('#toggleNotifications').attr('data-current-status'));
            console.log('Estado final do toggle de notificações - checked:', $('#toggleNotifications').prop('checked'));
            console.log('Estado final do badge de notificações - text:', $('#notifications-status').text());
            console.log('Estado final do badge de notificações - classes:', $('#notifications-status').attr('class'));
        }

        // Função para alternar o status das notificações
        function toggleNotificationsStatus() {
            const currentStatus = $('#toggleNotifications').attr('data-current-status');
            console.log('Alternando status das notificações. Status atual:', currentStatus);

            // URL completa para depuração
            const url = '{{ route("robots.toggle-start23") }}';
            console.log('URL da requisição para notificações:', url);

            $.ajax({
                url: url,
                type: 'POST',
                dataType: 'json',
                data: {
                    id: 1, // Assumindo que estamos trabalhando com o registro de ID 1
                    current_status: currentStatus,
                    field: 'notifications', // Campo específico para notificações
                    _token: '{{ csrf_token() }}'
                },
                beforeSend: function() {
                    console.log('Enviando requisição para alternar status das notificações...');
                    console.log('Dados enviados:', {
                        id: 1,
                        current_status: currentStatus,
                        field: 'notifications',
                        _token: '{{ csrf_token() }}'
                    });
                },
                success: function(response) {
                    console.log('Resposta recebida para notificações:', response);

                    if (response.success) {
                        console.log('Status das notificações alternado com sucesso. Novo status:', response.new_status);
                        updateNotificationsToggle(response.new_status);

                        // Mostrar mensagem de sucesso
                        alert(response.message || 'Status das notificações atualizado com sucesso.');
                    } else {
                        console.error('Erro ao atualizar o status das notificações:', response.message || 'Erro desconhecido');
                        alert('Erro ao atualizar o status das notificações.');
                    }
                },
                error: function(xhr, status, error) {
                    console.error('Erro na requisição para alternar status das notificações');
                    console.error('Status:', status);
                    console.error('Erro:', error);
                    console.error('Resposta:', xhr.responseText);

                    alert('Erro de comunicação com o servidor.');
                },
                complete: function() {
                    console.log('Requisição para alternar status das notificações finalizada');
                }
            });
        }

        // Inicia o carregamento dos dados quando a página estiver pronta
        $(document).ready(function() {
            console.log('Documento pronto, iniciando carregamento...');

            // Primeiro, carregar o status atual do Start23 (prioridade)
            loadStart23Status();

            // Carregar o status atual das notificações
            loadNotificationsStatus();

            // Iniciar os relógios digitais
            updateClocks();

            // Carregar dados e notícias
            loadLatestData();
            loadLatestNews();

            // Carregar estatísticas de velas
            loadCandleStats();
            loadLast50Candles();

            // Adicionar a carga das notícias econômicas
            loadEconomicNews();

            // Adicionar evento de clique aos botões de quantidade de registros
            $('.record-count-btn').on('click', function() {
                const count = $(this).data('count');
                loadCustomCountData(count);
            });

            // Adicionar evento de clique ao toggle de notificações
            $('#toggleNotifications').on('change', function() {
                toggleNotificationsStatus();
            });

            // Iniciar a verificação do cronômetro
            checkAndStartCountdown();

            // Adicionar evento de clique ao toggle
            $('#toggleStart23').on('change', function() {
                toggleStart23Status();
            });

            // Verificar periodicamente o status do Start23 (a cada 30 segundos)
            setInterval(function() {
                loadStart23Status();
            }, 30000);
        });
    </script>
</body>

</html>
