<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Teste do Sistema de Login</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #1a1a1a;
            color: #ffffff;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .container {
            margin-top: 50px;
        }
        .card {
            background-color: #2d2d2d;
            border: 1px solid #404040;
        }
        .card-header {
            background-color: #007bff;
            color: white;
        }
        .btn-primary {
            background-color: #007bff;
            border-color: #007bff;
        }
        .btn-success {
            background-color: #28a745;
            border-color: #28a745;
        }
        .btn-danger {
            background-color: #dc3545;
            border-color: #dc3545;
        }
        .alert-info {
            background-color: rgba(23, 162, 184, 0.1);
            border-color: #17a2b8;
            color: #b8daff;
        }
        .alert-success {
            background-color: rgba(40, 167, 69, 0.1);
            border-color: #28a745;
            color: #d4edda;
        }
        .alert-danger {
            background-color: rgba(220, 53, 69, 0.1);
            border-color: #dc3545;
            color: #f5c6cb;
        }
        .form-control {
            background-color: #404040;
            border-color: #555555;
            color: #ffffff;
        }
        .form-control:focus {
            background-color: #404040;
            border-color: #007bff;
            color: #ffffff;
            box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
        }
        .table-dark {
            background-color: #2d2d2d;
        }
        pre {
            background-color: #1a1a1a;
            border: 1px solid #404040;
            padding: 15px;
            border-radius: 5px;
            color: #ffffff;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="row">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">
                        <h4><i class="fas fa-cog"></i> Teste do Sistema de Login</h4>
                    </div>
                    <div class="card-body">
                        
                        <!-- Status da Autenticação -->
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <div class="alert alert-info">
                                    <h5><i class="fas fa-info-circle"></i> Status da Autenticação</h5>
                                    @if(Auth::check())
                                        <p><strong>✅ Usuário Logado:</strong> {{ Auth::user()->nome }}</p>
                                        <p><strong>Email:</strong> {{ Auth::user()->email }}</p>
                                        <p><strong>ID:</strong> {{ Auth::user()->id_usuario }}</p>
                                        <p><strong>Ativo:</strong> {{ Auth::user()->ativo ? 'Sim' : 'Não' }}</p>
                                        <form method="POST" action="{{ route('logout') }}" class="d-inline">
                                            @csrf
                                            <button type="submit" class="btn btn-danger btn-sm">
                                                <i class="fas fa-sign-out-alt"></i> Fazer Logout
                                            </button>
                                        </form>
                                    @else
                                        <p><strong>❌ Nenhum usuário logado</strong></p>
                                        <a href="{{ route('login') }}" class="btn btn-primary btn-sm">
                                            <i class="fas fa-sign-in-alt"></i> Ir para Login
                                        </a>
                                    @endif
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="alert alert-success">
                                    <h5><i class="fas fa-database"></i> Teste de Conexão</h5>
                                    @php
                                        try {
                                            $userCount = \App\Models\User::count();
                                            echo "<p><strong>✅ Banco conectado</strong></p>";
                                            echo "<p><strong>Total de usuários:</strong> $userCount</p>";
                                        } catch (Exception $e) {
                                            echo "<p><strong>❌ Erro na conexão:</strong> " . $e->getMessage() . "</p>";
                                        }
                                    @endphp
                                </div>
                            </div>
                        </div>

                        <!-- Criar Usuários de Teste -->
                        <div class="row mb-4">
                            <div class="col-md-12">
                                <div class="card">
                                    <div class="card-header">
                                        <h5><i class="fas fa-users"></i> Criar Usuários de Teste</h5>
                                    </div>
                                    <div class="card-body">
                                        <form method="POST" action="{{ url('/test-create-users') }}">
                                            @csrf
                                            <button type="submit" class="btn btn-success">
                                                <i class="fas fa-plus"></i> Criar Usuários de Teste
                                            </button>
                                        </form>
                                        
                                        @if(session('users_created'))
                                            <div class="alert alert-success mt-3">
                                                <h6>✅ Usuários criados com sucesso!</h6>
                                                <ul class="mb-0">
                                                    <li><strong>Admin:</strong> <EMAIL> / 123456</li>
                                                    <li><strong>Trader:</strong> <EMAIL> / trader123</li>
                                                </ul>
                                            </div>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Lista de Usuários -->
                        <div class="row mb-4">
                            <div class="col-md-12">
                                <div class="card">
                                    <div class="card-header">
                                        <h5><i class="fas fa-list"></i> Usuários Cadastrados</h5>
                                    </div>
                                    <div class="card-body">
                                        @php
                                            try {
                                                $users = \App\Models\User::all();
                                            } catch (Exception $e) {
                                                $users = collect();
                                                echo "<div class='alert alert-danger'>Erro ao buscar usuários: " . $e->getMessage() . "</div>";
                                            }
                                        @endphp
                                        
                                        @if($users->count() > 0)
                                            <div class="table-responsive">
                                                <table class="table table-dark table-striped">
                                                    <thead>
                                                        <tr>
                                                            <th>ID</th>
                                                            <th>Nome</th>
                                                            <th>Email</th>
                                                            <th>Ativo</th>
                                                            <th>Ocupação</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        @foreach($users as $user)
                                                            <tr>
                                                                <td>{{ $user->id_usuario }}</td>
                                                                <td>{{ $user->nome }}</td>
                                                                <td>{{ $user->email }}</td>
                                                                <td>
                                                                    @if($user->ativo)
                                                                        <span class="badge bg-success">Ativo</span>
                                                                    @else
                                                                        <span class="badge bg-danger">Inativo</span>
                                                                    @endif
                                                                </td>
                                                                <td>{{ $user->ocupacao ?? '-' }}</td>
                                                            </tr>
                                                        @endforeach
                                                    </tbody>
                                                </table>
                                            </div>
                                        @else
                                            <div class="alert alert-warning">
                                                <p>Nenhum usuário encontrado. Clique em "Criar Usuários de Teste" para começar.</p>
                                            </div>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Links de Navegação -->
                        <div class="row">
                            <div class="col-md-12">
                                <div class="card">
                                    <div class="card-header">
                                        <h5><i class="fas fa-link"></i> Links de Teste</h5>
                                    </div>
                                    <div class="card-body">
                                        <a href="{{ route('login') }}" class="btn btn-primary me-2">
                                            <i class="fas fa-sign-in-alt"></i> Página de Login
                                        </a>
                                        @if(Auth::check())
                                            <a href="{{ route('ativos.index') }}" class="btn btn-info me-2">
                                                <i class="fas fa-chart-line"></i> Dashboard Ativos
                                            </a>
                                            <a href="{{ route('ativos.dashboard') }}" class="btn btn-success me-2">
                                                <i class="fas fa-tachometer-alt"></i> Dashboard Trading
                                            </a>
                                            <a href="{{ route('brokers.index') }}" class="btn btn-warning">
                                                <i class="fas fa-building"></i> Gestão de Brokers
                                            </a>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        </div>

                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/js/all.min.js"></script>
</body>
</html>
