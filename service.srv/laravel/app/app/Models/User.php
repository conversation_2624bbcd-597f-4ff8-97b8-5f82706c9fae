<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Illuminate\Support\Facades\Hash;

class User extends Authenticatable
{
    use HasFactory, Notifiable;

    protected $table = 'tbl_usuarios';
    protected $primaryKey = 'id_usuario';

    // Desabilitar timestamps se não existirem as colunas created_at e updated_at
    public $timestamps = false;

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'token',
        'nome',
        'email',
        'senha',
        'ativo',
        'phone',
        'ocupacao',
        'plan_id',
        'plan_expiry_date'
    ];

    /**
     * The attributes that should be hidden for serialization.
     */
    protected $hidden = [
        'senha',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     */
    protected $casts = [
        'ativo' => 'boolean',
        'plan_expiry_date' => 'datetime',
    ];

    /**
     * Get the password for authentication.
     * <PERSON><PERSON> expects 'password' but our table has 'senha'
     */
    public function getAuthPassword()
    {
        return $this->senha;
    }

    /**
     * Get the name of the unique identifier for the user.
     */
    public function getAuthIdentifierName()
    {
        return 'email';
    }

    /**
     * Get the unique identifier for the user.
     */
    public function getAuthIdentifier()
    {
        return $this->email;
    }

    /**
     * Verificar se o usuário está ativo
     */
    public function isActive()
    {
        return $this->ativo == 1;
    }

    /**
     * Scope para usuários ativos
     */
    public function scopeActive($query)
    {
        return $query->where('ativo', 1);
    }

    /**
     * Accessor para o nome completo
     */
    public function getNameAttribute()
    {
        return $this->nome;
    }
}
