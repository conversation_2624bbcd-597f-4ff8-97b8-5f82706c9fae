<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Broker extends Model
{
    use HasFactory;

    protected $table = 'brokers';

    // Desabilitar timestamps se não existirem as colunas created_at e updated_at
    public $timestamps = false;

    protected $fillable = [
        'name',
        'username',
        'password',
        'opening_date',
        'is_active',
        'account_limit',
        'account_type',
        'is_prop_firm',
        'prop_status'
    ];

    protected $casts = [
        'opening_date' => 'date',
        'is_active' => 'boolean',
        'is_prop_firm' => 'boolean',
        'account_limit' => 'decimal:2'
    ];

    // Accessor para formatar o limite da conta
    public function getFormattedAccountLimitAttribute()
    {
        return number_format($this->account_limit, 2, ',', '.');
    }

    // Accessor para status formatado
    public function getStatusTextAttribute()
    {
        return $this->is_active ? 'Ativo' : 'Inativo';
    }

    // Accessor para tipo de conta formatado
    public function getAccountTypeTextAttribute()
    {
        return $this->account_type;
    }

    // Accessor para prop firm formatado
    public function getPropFirmTextAttribute()
    {
        return $this->is_prop_firm ? 'Sim' : 'Não';
    }

    // Scope para brokers ativos
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    // Scope para brokers inativos
    public function scopeInactive($query)
    {
        return $query->where('is_active', false);
    }

    // Scope para prop firms
    public function scopePropFirms($query)
    {
        return $query->where('is_prop_firm', true);
    }
}
