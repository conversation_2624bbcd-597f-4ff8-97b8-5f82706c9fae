<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;

class AnalyticsController extends Controller
{
    /**
     * Display the analytics dashboard.
     */
    public function index()
    {
        // Dados mock para demonstração
        $analyticsData = [
            'net_profit' => 8400,
            'sharpe_ratio' => 1.21,
            'win_rate' => 70.2,
            'risk_reward_ratio' => '1:1.32',
            'profit_factor' => 1.37,
            'best_trade' => 4421.3,
            'worst_trade' => -170.2,
            'total_trades' => 30,
            'avg_duration' => '00h 24m 12s',
            'total_commissions' => 22.51,
            'current_balance' => 7489,
            'highest_balance' => 9119,
            'lowest_balance' => 4107,
            'performance_data' => [
                'labels' => ['May 1', 'May 5', 'May 10', 'May 15', 'May 20', 'May 25', 'May 30'],
                'values' => [5000, 4200, 5800, 7200, 6800, 8400, 7489]
            ],
            'market_allocation' => [
                'forex' => 60,
                'crypto' => 25,
                'stocks' => 15
            ],
            'asset_allocation' => [
                'eur_usd' => 45,
                'gbp_usd' => 35,
                'usd_jpy' => 20
            ],
            'profit_contribution' => [
                'eur_usd' => 50,
                'gbp_usd' => 30,
                'usd_jpy' => 20
            ],
            'loss_contribution' => [
                'usd_jpy' => 40,
                'eur_usd' => 35,
                'gbp_usd' => 25
            ]
        ];

        return view('analytics.index', compact('analyticsData'));
    }

    /**
     * Get analytics data via API.
     */
    public function getAnalyticsData(Request $request)
    {
        // Aqui você pode implementar filtros baseados nos parâmetros da request
        $filters = [
            'account' => $request->get('account', 'all'),
            'asset' => $request->get('asset', 'all'),
            'result' => $request->get('result', 'all'),
            'move' => $request->get('move', 'all'),
            'strategy' => $request->get('strategy', 'all'),
            'date_from' => $request->get('date_from'),
            'date_to' => $request->get('date_to')
        ];

        // Dados mock que podem ser filtrados
        $data = [
            'summary' => [
                'net_profit' => 8400,
                'sharpe_ratio' => 1.21,
                'win_rate' => 70.2,
                'risk_reward_ratio' => 1.32,
                'profit_factor' => 1.37,
                'best_trade' => 4421.3,
                'worst_trade' => -170.2,
                'total_trades' => 30,
                'avg_duration' => 1452, // em segundos
                'total_commissions' => 22.51
            ],
            'performance' => [
                'daily_pnl' => [
                    ['date' => '2023-05-01', 'pnl' => 200],
                    ['date' => '2023-05-02', 'pnl' => -800],
                    ['date' => '2023-05-03', 'pnl' => 1600],
                    ['date' => '2023-05-04', 'pnl' => 1400],
                    ['date' => '2023-05-05', 'pnl' => -400],
                    ['date' => '2023-05-06', 'pnl' => 1600],
                    ['date' => '2023-05-07', 'pnl' => -911]
                ],
                'balance_curve' => [
                    ['date' => '2023-05-01', 'balance' => 5000],
                    ['date' => '2023-05-02', 'balance' => 4200],
                    ['date' => '2023-05-03', 'balance' => 5800],
                    ['date' => '2023-05-04', 'balance' => 7200],
                    ['date' => '2023-05-05', 'balance' => 6800],
                    ['date' => '2023-05-06', 'balance' => 8400],
                    ['date' => '2023-05-07', 'balance' => 7489]
                ]
            ],
            'allocations' => [
                'market' => [
                    ['name' => 'Forex', 'percentage' => 60, 'trades' => 18],
                    ['name' => 'Crypto', 'percentage' => 25, 'trades' => 8],
                    ['name' => 'Stocks', 'percentage' => 15, 'trades' => 4]
                ],
                'assets' => [
                    ['name' => 'EUR/USD', 'percentage' => 45, 'trades' => 14],
                    ['name' => 'GBP/USD', 'percentage' => 35, 'trades' => 10],
                    ['name' => 'USD/JPY', 'percentage' => 20, 'trades' => 6]
                ]
            ],
            'trades' => [
                [
                    'id' => 1,
                    'asset' => 'EUR/USD',
                    'type' => 'Long',
                    'entry_price' => 1.0850,
                    'exit_price' => 1.0920,
                    'quantity' => 10000,
                    'pnl' => 700,
                    'duration' => '2h 15m',
                    'date' => '2023-05-07 09:30:00'
                ],
                [
                    'id' => 2,
                    'asset' => 'GBP/USD',
                    'type' => 'Short',
                    'entry_price' => 1.2450,
                    'exit_price' => 1.2380,
                    'quantity' => 5000,
                    'pnl' => 350,
                    'duration' => '1h 45m',
                    'date' => '2023-05-07 11:15:00'
                ],
                [
                    'id' => 3,
                    'asset' => 'USD/JPY',
                    'type' => 'Long',
                    'entry_price' => 134.20,
                    'exit_price' => 133.85,
                    'quantity' => 8000,
                    'pnl' => -280,
                    'duration' => '45m',
                    'date' => '2023-05-07 14:30:00'
                ]
            ]
        ];

        return response()->json($data);
    }

    /**
     * Export analytics data.
     */
    public function export(Request $request)
    {
        $format = $request->get('format', 'csv'); // csv, xlsx, pdf
        
        // Implementar lógica de exportação
        // Por enquanto retorna uma resposta mock
        
        return response()->json([
            'message' => "Exportação em formato {$format} iniciada",
            'download_url' => '/downloads/analytics_' . date('Y-m-d') . '.' . $format
        ]);
    }
}
