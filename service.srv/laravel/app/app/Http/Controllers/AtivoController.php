<?php

namespace App\Http\Controllers;

use App\Models\Ativo;
use Illuminate\Http\Request;

class AtivoController extends Controller
{
    public function index()
    {
        $ativos = Ativo::latest()->take(200)->get();
        return view('ativos.index', compact('ativos'));
    }

    public function dashboard()
    {
        return view('ativos.dashboard');
    }

    public function getLatestData()
    {
        $ativos = Ativo::latest()->take(200)->get();
        return response()->json($ativos);
    }

    /**
     * Retorna os últimos 2000 registros para análise detalhada
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getExtendedData()
    {
        // Buscar os últimos 2000 registros
        $ativos = Ativo::latest()->take(3000)->get();
        return response()->json($ativos);
    }

    /**
     * Retorna um número específico de registros para análise
     *
     * @param int $count Número de registros a serem retornados
     * @return \Illuminate\Http\JsonResponse
     */
    public function getCustomCountData($count)
    {
        // Validar o número de registros (entre 100 e 5000)
        $count = max(100, min(5000, intval($count)));

        // Buscar os registros
        $ativos = Ativo::latest()->take($count)->get();
        return response()->json($ativos);
    }
}
