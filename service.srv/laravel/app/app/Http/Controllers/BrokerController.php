<?php

namespace App\Http\Controllers;

use App\Models\Broker;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;

class BrokerController extends Controller
{
    /**
     * Exibe a página principal de gestão de brokers
     */
    public function index()
    {
        $brokers = Broker::orderBy('id', 'desc')->get();
        return view('brokers.index', compact('brokers'));
    }

    /**
     * Armazena um novo broker
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:100',
            'username' => 'required|string|max:100',
            'password' => 'required|string',
            'opening_date' => 'required|date',
            'is_active' => 'required|boolean',
            'account_limit' => 'required|numeric|min:0',
            'account_type' => 'required|in:Demo,Real',
            'is_prop_firm' => 'required|boolean',
            'prop_status' => 'nullable|in:Approved,Rejected'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $broker = Broker::create($request->all());

            return response()->json([
                'success' => true,
                'message' => 'Broker criado com sucesso!',
                'broker' => $broker
            ]);
        } catch (\Exception $e) {
            Log::error('Erro ao criar broker: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => 'Erro interno do servidor'
            ], 500);
        }
    }

    /**
     * Retorna os dados de um broker específico para edição
     */
    public function show($id)
    {
        try {
            $broker = Broker::findOrFail($id);

            return response()->json([
                'success' => true,
                'broker' => $broker
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Broker não encontrado'
            ], 404);
        }
    }

    /**
     * Atualiza um broker existente
     */
    public function update(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:100',
            'username' => 'required|string|max:100',
            'password' => 'required|string',
            'opening_date' => 'required|date',
            'is_active' => 'required|boolean',
            'account_limit' => 'required|numeric|min:0',
            'account_type' => 'required|in:Demo,Real',
            'is_prop_firm' => 'required|boolean',
            'prop_status' => 'nullable|in:Approved,Rejected'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $broker = Broker::findOrFail($id);
            $broker->update($request->all());

            return response()->json([
                'success' => true,
                'message' => 'Broker atualizado com sucesso!',
                'broker' => $broker
            ]);
        } catch (\Exception $e) {
            Log::error('Erro ao atualizar broker: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => 'Erro interno do servidor'
            ], 500);
        }
    }

    /**
     * Remove um broker
     */
    public function destroy($id)
    {
        try {
            $broker = Broker::findOrFail($id);
            $broker->delete();

            return response()->json([
                'success' => true,
                'message' => 'Broker removido com sucesso!'
            ]);
        } catch (\Exception $e) {
            Log::error('Erro ao remover broker: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => 'Erro interno do servidor'
            ], 500);
        }
    }

    /**
     * Retorna todos os brokers em formato JSON
     */
    public function getBrokers()
    {
        try {
            $brokers = Broker::orderBy('id', 'desc')->get();

            return response()->json([
                'success' => true,
                'brokers' => $brokers
            ]);
        } catch (\Exception $e) {
            Log::error('Erro ao buscar brokers: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => 'Erro interno do servidor'
            ], 500);
        }
    }
}
