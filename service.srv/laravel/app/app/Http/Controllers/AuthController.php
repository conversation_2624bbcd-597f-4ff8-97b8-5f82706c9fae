<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use App\Models\User;

class AuthController extends Controller
{
    /**
     * Exibe a página de login
     */
    public function showLoginForm()
    {
        // Se já estiver logado, redireciona para o dashboard
        if (Auth::check()) {
            return redirect()->route('ativos.index');
        }
        
        return view('auth.login');
    }

    /**
     * Processa o login
     */
    public function login(Request $request)
    {
        $request->validate([
            'email' => 'required|email',
            'password' => 'required|min:6',
        ], [
            'email.required' => 'O campo email é obrigatório.',
            'email.email' => 'Digite um email válido.',
            'password.required' => 'O campo senha é obrigatório.',
            'password.min' => 'A senha deve ter pelo menos 6 caracteres.',
        ]);

        // Buscar usuário pelo email
        $user = User::where('email', $request->email)->first();

        if (!$user) {
            return back()->withErrors([
                'email' => 'Email não encontrado.',
            ])->withInput($request->only('email'));
        }

        // Verificar se o usuário está ativo
        if (!$user->isActive()) {
            return back()->withErrors([
                'email' => 'Usuário inativo. Entre em contato com o administrador.',
            ])->withInput($request->only('email'));
        }

        // Verificar a senha usando password_hash
        if (!Hash::check($request->password, $user->senha)) {
            return back()->withErrors([
                'password' => 'Senha incorreta.',
            ])->withInput($request->only('email'));
        }

        // Fazer login
        Auth::login($user, $request->filled('remember'));

        // Regenerar sessão para segurança
        $request->session()->regenerate();

        // Redirecionar para o dashboard
        return redirect()->intended(route('ativos.index'));
    }

    /**
     * Processa o logout
     */
    public function logout(Request $request)
    {
        Auth::logout();

        $request->session()->invalidate();
        $request->session()->regenerateToken();

        return redirect()->route('login')->with('success', 'Logout realizado com sucesso!');
    }

    /**
     * Exibe a página de registro (opcional)
     */
    public function showRegisterForm()
    {
        return view('auth.register');
    }

    /**
     * Processa o registro (opcional)
     */
    public function register(Request $request)
    {
        $request->validate([
            'nome' => 'required|string|max:100',
            'email' => 'required|string|email|max:45|unique:tbl_usuarios,email',
            'password' => 'required|string|min:6|confirmed',
            'phone' => 'nullable|string|max:45',
            'ocupacao' => 'nullable|string|max:50',
        ], [
            'nome.required' => 'O campo nome é obrigatório.',
            'email.required' => 'O campo email é obrigatório.',
            'email.email' => 'Digite um email válido.',
            'email.unique' => 'Este email já está cadastrado.',
            'password.required' => 'O campo senha é obrigatório.',
            'password.min' => 'A senha deve ter pelo menos 6 caracteres.',
            'password.confirmed' => 'A confirmação da senha não confere.',
        ]);

        // Criar usuário
        $user = User::create([
            'nome' => $request->nome,
            'email' => $request->email,
            'senha' => Hash::make($request->password),
            'phone' => $request->phone,
            'ocupacao' => $request->ocupacao,
            'ativo' => 1,
            'token' => \Str::random(30),
        ]);

        // Fazer login automático
        Auth::login($user);

        return redirect()->route('ativos.index')->with('success', 'Conta criada com sucesso!');
    }
}
